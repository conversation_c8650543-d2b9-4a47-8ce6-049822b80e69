/**
 * 提示词片段编辑器模块 - Refactored
 */
class PromptEditor {
    constructor(configManager, fieldMapper, promptComposer, localStorageManager, geminiService) {
        if (!configManager || !fieldMapper || !promptComposer || !localStorageManager || !geminiService) {
            throw new Error("PromptEditor requires all dependencies to be injected.");
        }
        this.configManager = configManager;
        this.fieldMapper = fieldMapper;
        this.promptComposer = promptComposer;
        this.localStorageManager = localStorageManager;
        this.geminiService = geminiService;

        this.promptSnippets = this.loadDefaultSnippets();
        this.requiredFields = this.fieldMapper.getApiFieldDefinitions().required || [];
        this.optionalFields = this.fieldMapper.getApiFieldDefinitions().optional || [];
        this.fieldDefinitions = this.fieldMapper.getApiFieldDefinitions().descriptions || {};

        this.aiOptimizer = new AIOptimizer(this, this.geminiService);

        // 性能优化相关
        this.searchCache = new Map(); // 搜索结果缓存
        this.renderCache = new Map(); // 渲染缓存
        this.isEditorInitialized = false; // 懒加载标记
        this.debounceTimers = new Map(); // 防抖定时器

        this.initializeEditor();
        setTimeout(() => {
            this.loadAllSnippetsFromStorage();
            this.initializeMainInterfaceIntegration();
        }, 1000);
    }

    initializeEditor() {
        console.log('PromptEditor initialized (Refactored)');
    }

    /**
     * 加载默认的提示词片段
     * @returns {Array} 默认提示词片段数组
     */
    loadDefaultSnippets() {
        return [
            {
                id: 'default_booking_extract',
                name: '基础订单信息提取',
                template: '请从以下订单文本中提取关键信息：\n{input}\n\n请提取并返回以下字段：\n- 客户姓名\n- 联系电话\n- 服务日期\n- 服务类型',
                category: 'extraction',
                usage: 'booking_info'
            },
            {
                id: 'channel_detection',
                name: '渠道检测',
                template: '请识别以下订单来源于哪个平台：\n{input}\n\n可能的平台包括：Klook, KKday, 携程, 途牛等。',
                category: 'detection',
                usage: 'channel_detection'
            }
        ];
    }

    /**
     * 数据适配层：将LocalStorageManager的对象树格式转换为编辑器数组格式
     * @UTIL 数据格式适配工具
     * @param {Object} promptsObject - LocalStorageManager返回的对象树 {channel: {field: {content, ...}}}
     * @returns {Array} 编辑器使用的片段数组格式
     */
    mapPromptsObjectToSnippetsArray(promptsObject) {
        const snippets = [];

        if (!promptsObject || typeof promptsObject !== 'object') {
            return snippets;
        }

        // 遍历渠道和字段，转换为片段数组
        Object.entries(promptsObject).forEach(([channel, fields]) => {
            if (fields && typeof fields === 'object') {
                Object.entries(fields).forEach(([field, promptData]) => {
                    if (promptData && promptData.content) {
                        snippets.push({
                            id: `${channel}_${field}_${Date.now()}`, // 生成唯一ID
                            name: promptData.name || `${channel} - ${field}`, // 显示名称
                            template: promptData.content, // 提示词内容
                            category: this.inferCategory(field), // 推断分类
                            usage: promptData.usage || field, // 使用场景
                            channel: channel, // 所属渠道
                            field: field, // 对应字段
                            createdAt: promptData.createdAt || new Date().toISOString(),
                            updatedAt: promptData.updatedAt || new Date().toISOString()
                        });
                    }
                });
            }
        });

        return snippets;
    }

    /**
     * 推断片段分类
     * @UTIL 分类推断工具
     * @param {string} field - 字段名
     * @returns {string} 分类名称
     */
    inferCategory(field) {
        const extractionFields = ['customer', 'phone', 'email', 'address', 'date', 'time'];
        const detectionFields = ['channel', 'platform', 'source'];

        if (extractionFields.some(f => field.toLowerCase().includes(f))) {
            return 'extraction';
        }
        if (detectionFields.some(f => field.toLowerCase().includes(f))) {
            return 'detection';
        }
        return 'custom';
    }

    /**
     * 数据适配层：将编辑器片段格式转换为存储格式
     * @UTIL 数据格式适配工具
     * @param {Object} snippet - 编辑器片段对象
     * @returns {Object} 存储格式的数据
     */
    mapSnippetToStorage(snippet) {
        return {
            content: snippet.template,
            field: snippet.field,
            channel: snippet.channel,
            name: snippet.name,
            usage: snippet.usage,
            category: snippet.category,
            createdAt: snippet.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
    }

    /**
     * 从本地存储加载所有提示词片段
     * @SERVICE 提示词片段加载服务
     */
    loadAllSnippetsFromStorage() {
        try {
            const storedPromptsObject = this.localStorageManager.getAllPrompts();
            console.log('📥 从存储加载的原始数据:', storedPromptsObject);

            // 使用适配层转换数据格式
            const storedSnippets = this.mapPromptsObjectToSnippetsArray(storedPromptsObject);

            // 合并默认片段和存储的片段
            this.promptSnippets = [...this.loadDefaultSnippets(), ...storedSnippets];

            console.log('✅ 提示词片段加载完成:', this.promptSnippets.length, '个片段');
            console.log('📋 片段详情:', this.promptSnippets.map(s => `${s.name} (${s.channel}/${s.field})`));
        } catch (error) {
            console.warn('⚠️ 加载提示词片段失败:', error);
            this.promptSnippets = this.loadDefaultSnippets();
        }
    }

    /**
     * 保存片段到本地存储
     * @SERVICE 片段保存服务
     * @param {Object} snippet - 要保存的片段
     */
    saveSnippetToStorage(snippet) {
        try {
            const storageData = this.mapSnippetToStorage(snippet);
            const result = this.localStorageManager.savePrompt(
                snippet.channel,
                snippet.field,
                storageData.content
            );

            console.log('💾 片段保存成功:', snippet.name, result);

            // 触发缓存清理和系统更新
            this.triggerSystemUpdate('snippet_saved', snippet);

            return result;
        } catch (error) {
            console.error('❌ 保存片段失败:', error);
            throw error;
        }
    }

    /**
     * 从存储中删除片段
     * @SERVICE 片段删除服务
     * @param {Object} snippet - 要删除的片段
     */
    deleteSnippetFromStorage(snippet) {
        try {
            const result = this.localStorageManager.deletePrompt(snippet.channel, snippet.field);
            console.log('🗑️ 片段删除成功:', snippet.name);

            // 触发缓存清理和系统更新
            this.triggerSystemUpdate('snippet_deleted', snippet);

            return result;
        } catch (error) {
            console.error('❌ 删除片段失败:', error);
            throw error;
        }
    }

    /**
     * 打开提示词编辑器主界面
     * @SERVICE 编辑器界面服务
     * @LIFECYCLE 编辑器生命周期管理
     * @DEPENDENCY 依赖 DOM 操作、事件绑定、数据加载
     *
     * 功能说明：
     * - 创建模态框编辑器界面
     * - 初始化三栏布局（片段列表|编辑区|AI建议区）
     * - 绑定所有交互事件
     * - 加载现有片段数据
     */
    openEditor() {
        console.log('📝 打开提示词编辑器');
        this.closeEditor(); // 确保关闭已存在的编辑器，避免重复实例
        this.createEditorModal(); // 创建编辑器界面
    }

    /**
     * 关闭编辑器
     * @LIFECYCLE 编辑器生命周期管理
     */
    closeEditor() {
        const existingModal = document.getElementById('prompt-editor-modal');
        if (existingModal) {
            existingModal.remove();
        }
    }

    /**
     * 创建编辑器模态框（懒加载优化）
     * @LIFECYCLE 编辑器界面创建
     */
    createEditorModal() {
        // 懒加载：仅在首次打开时初始化
        if (!this.isEditorInitialized) {
            console.log('🚀 首次初始化编辑器界面...');
            this.isEditorInitialized = true;
        }

        const modal = document.createElement('div');
        modal.id = 'prompt-editor-modal';
        modal.className = 'prompt-editor-modal';

        modal.innerHTML = this.getEditorHTML();
        document.body.appendChild(modal);

        // 绑定事件
        this.bindEditorEvents();

        // 点击遮罩关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeEditor();
            }
        });

        // 初始化界面数据
        this.initializeEditorData();
    }

    /**
     * 获取编辑器HTML结构
     * @UTIL 界面模板生成
     */
    getEditorHTML() {
        return `
            <div class="prompt-editor-content">
                <div class="prompt-editor-header">
                    <h2>📝 提示词片段编辑器</h2>
                    <button class="prompt-editor-close" id="pe-close-btn">×</button>
                </div>

                <div class="prompt-editor-body">
                    <!-- 左侧片段列表 -->
                    <div class="prompt-snippets-panel">
                        <div class="snippets-header">
                            <input type="text" class="snippets-search" id="pe-search" placeholder="搜索片段...">
                            <div class="snippets-filters" id="pe-filters">
                                <button class="filter-tag active" data-filter="all">全部</button>
                                <button class="filter-tag" data-filter="extraction">提取</button>
                                <button class="filter-tag" data-filter="detection">检测</button>
                                <button class="filter-tag" data-filter="custom">自定义</button>
                            </div>
                        </div>
                        <div class="snippets-list" id="pe-snippets-list">
                            <!-- 片段列表将在这里动态生成 -->
                        </div>
                        <div style="padding: 10px;">
                            <button class="btn btn-primary btn-sm" id="pe-add-snippet" style="width: 100%; margin-bottom: 8px;">+ 新增片段</button>
                            <div style="display: flex; gap: 4px;">
                                <button class="btn btn-secondary btn-sm" id="pe-export-all" style="flex: 1; font-size: 11px;">📤 导出</button>
                                <button class="btn btn-secondary btn-sm" id="pe-import-data" style="flex: 1; font-size: 11px;">📥 导入</button>
                            </div>
                        </div>
                    </div>

                    <!-- 中间编辑区域 -->
                    <div class="prompt-editor-panel">
                        <div class="editor-header">
                            <h3 id="pe-editor-title">选择一个片段进行编辑</h3>
                        </div>
                        <div class="editor-form" id="pe-editor-form">
                            <div class="form-group">
                                <label class="form-label">片段名称</label>
                                <input type="text" class="form-input" id="pe-snippet-name" placeholder="输入片段名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">所属渠道</label>
                                <input type="text" class="form-input" id="pe-snippet-channel" placeholder="如: klook, kkday, generic">
                            </div>
                            <div class="form-group">
                                <label class="form-label">对应字段</label>
                                <input type="text" class="form-input" id="pe-snippet-field" placeholder="如: customer, phone, address">
                            </div>
                            <div class="form-group">
                                <label class="form-label">分类</label>
                                <select class="form-select" id="pe-snippet-category">
                                    <option value="extraction">信息提取</option>
                                    <option value="detection">渠道检测</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">使用场景</label>
                                <input type="text" class="form-input" id="pe-snippet-usage" placeholder="描述使用场景">
                            </div>
                            <div class="form-group">
                                <label class="form-label">提示词模板</label>
                                <textarea class="form-textarea" id="pe-snippet-template" placeholder="输入提示词模板内容..."></textarea>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" id="pe-save-btn">💾 保存</button>
                            <button class="btn btn-danger" id="pe-delete-btn">🗑️ 删除</button>
                            <button class="btn btn-secondary" id="pe-reset-btn">🔄 重置</button>
                        </div>
                    </div>

                    <!-- 右侧AI建议区 -->
                    <div class="ai-suggestions-panel">
                        <div class="ai-header">
                            <h3>🤖 AI 优化建议</h3>
                            <button class="btn btn-sm btn-primary" id="pe-ai-analyze">分析优化</button>
                        </div>
                        <div class="ai-content" id="pe-ai-content">
                            <div style="text-align: center; color: #666; padding: 40px 20px;">
                                <p>选择一个片段后，点击"分析优化"获取AI建议</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定编辑器事件
     * @EVENT_HANDLER 编辑器事件绑定
     */
    bindEditorEvents() {
        // 关闭按钮
        document.getElementById('pe-close-btn').addEventListener('click', () => this.closeEditor());

        // 搜索功能（防抖优化）
        document.getElementById('pe-search').addEventListener('input', (e) => {
            this.debouncedFilterSnippets(e.target.value);
        });

        // 分类过滤
        document.querySelectorAll('#pe-filters .filter-tag').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 更新激活状态
                document.querySelectorAll('#pe-filters .filter-tag').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                // 执行过滤
                this.filterSnippetsByCategory(e.target.dataset.filter);
            });
        });

        // 新增片段
        document.getElementById('pe-add-snippet').addEventListener('click', () => this.createNewSnippet());

        // 表单操作
        document.getElementById('pe-save-btn').addEventListener('click', () => this.saveCurrentSnippet());
        document.getElementById('pe-delete-btn').addEventListener('click', () => this.deleteCurrentSnippet());
        document.getElementById('pe-reset-btn').addEventListener('click', () => this.resetForm());

        // AI分析
        document.getElementById('pe-ai-analyze').addEventListener('click', () => this.analyzeCurrentSnippet());

        // 导入导出
        document.getElementById('pe-export-all').addEventListener('click', () => this.showExportOptions());
        document.getElementById('pe-import-data').addEventListener('click', () => this.showImportDialog());
    }

    /**
     * 初始化编辑器数据
     * @LIFECYCLE 编辑器数据初始化
     */
    initializeEditorData() {
        this.currentSnippet = null; // 当前编辑的片段
        this.filteredSnippets = [...this.promptSnippets]; // 过滤后的片段列表
        this.renderSnippetsList();
    }

    /**
     * 渲染片段列表（性能优化版）
     * @SERVICE 片段列表渲染服务
     */
    renderSnippetsList() {
        const listContainer = document.getElementById('pe-snippets-list');

        if (this.filteredSnippets.length === 0) {
            listContainer.innerHTML = `
                <div style="text-align: center; color: #666; padding: 20px;">
                    <p>暂无片段</p>
                    <p style="font-size: 12px;">点击下方"新增片段"开始创建</p>
                </div>
            `;
            return;
        }

        // 性能优化：大量数据时使用虚拟滚动
        if (this.filteredSnippets.length > 100) {
            this.renderVirtualizedList(listContainer);
        } else {
            this.renderStandardList(listContainer);
        }
    }

    /**
     * 渲染标准列表（少量数据）
     * @SERVICE 标准列表渲染服务
     */
    renderStandardList(container) {
        const renderKey = `standard_${this.filteredSnippets.length}_${Date.now()}`;

        // 检查渲染缓存
        if (this.renderCache.has(renderKey)) {
            container.innerHTML = this.renderCache.get(renderKey);
        } else {
            const html = this.filteredSnippets.map(snippet => this.getSnippetItemHTML(snippet)).join('');
            container.innerHTML = html;

            // 缓存渲染结果
            this.renderCache.set(renderKey, html);

            // 限制缓存大小
            if (this.renderCache.size > 20) {
                const firstKey = this.renderCache.keys().next().value;
                this.renderCache.delete(firstKey);
            }
        }

        // 绑定事件
        this.bindSnippetItemEvents(container);
    }

    /**
     * 渲染虚拟化列表（大量数据）
     * @SERVICE 虚拟化列表渲染服务
     */
    renderVirtualizedList(container) {
        const itemHeight = 60; // 每个项目的高度
        const containerHeight = container.clientHeight || 400;
        const visibleCount = Math.ceil(containerHeight / itemHeight) + 5; // 多渲染5个作为缓冲

        // 创建虚拟滚动容器
        container.innerHTML = `
            <div class="virtual-scroll-container" style="height: ${this.filteredSnippets.length * itemHeight}px; position: relative;">
                <div class="virtual-scroll-content" style="position: absolute; top: 0; left: 0; right: 0;"></div>
            </div>
        `;

        const scrollContainer = container.querySelector('.virtual-scroll-container');
        const contentContainer = container.querySelector('.virtual-scroll-content');

        let scrollTop = 0;

        const updateVisibleItems = () => {
            const startIndex = Math.floor(scrollTop / itemHeight);
            const endIndex = Math.min(startIndex + visibleCount, this.filteredSnippets.length);

            const visibleSnippets = this.filteredSnippets.slice(startIndex, endIndex);
            const html = visibleSnippets.map((snippet, index) => {
                const actualIndex = startIndex + index;
                return `<div style="position: absolute; top: ${actualIndex * itemHeight}px; left: 0; right: 0; height: ${itemHeight}px;">${this.getSnippetItemHTML(snippet)}</div>`;
            }).join('');

            contentContainer.innerHTML = html;
            this.bindSnippetItemEvents(contentContainer);
        };

        // 监听滚动事件
        container.addEventListener('scroll', () => {
            scrollTop = container.scrollTop;
            updateVisibleItems();
        });

        // 初始渲染
        updateVisibleItems();

        console.log(`📊 虚拟化渲染: ${this.filteredSnippets.length} 个片段，显示 ${visibleCount} 个`);
    }

    /**
     * 获取片段项目HTML
     * @UTIL 片段项目HTML生成工具
     */
    getSnippetItemHTML(snippet) {
        return `
            <div class="snippet-item" data-snippet-id="${snippet.id}">
                <div class="snippet-name">${this.escapeHtml(snippet.name)}</div>
                <div class="snippet-meta">
                    <span>${this.escapeHtml(snippet.channel)}/${this.escapeHtml(snippet.field)}</span>
                    <span class="category-${snippet.category}">${this.getCategoryLabel(snippet.category)}</span>
                </div>
            </div>
        `;
    }

    /**
     * 绑定片段项目事件
     * @EVENT_HANDLER 片段项目事件绑定
     */
    bindSnippetItemEvents(container) {
        container.querySelectorAll('.snippet-item').forEach(item => {
            item.addEventListener('click', () => {
                const snippetId = item.dataset.snippetId;
                this.selectSnippet(snippetId);
            });
        });
    }

    /**
     * 获取分类标签
     * @UTIL 分类标签工具
     */
    getCategoryLabel(category) {
        const labels = {
            'extraction': '提取',
            'detection': '检测',
            'custom': '自定义'
        };
        return labels[category] || category;
    }

    /**
     * 防抖过滤片段
     * @SERVICE 防抖搜索服务
     */
    debouncedFilterSnippets(searchTerm) {
        // 清除之前的定时器
        if (this.debounceTimers.has('search')) {
            clearTimeout(this.debounceTimers.get('search'));
        }

        // 设置新的防抖定时器
        const timer = setTimeout(() => {
            this.filterSnippets(searchTerm);
            this.debounceTimers.delete('search');
        }, 300); // 300ms 防抖延迟

        this.debounceTimers.set('search', timer);
    }

    /**
     * 过滤片段（按搜索关键词，带缓存优化）
     * @SERVICE 片段搜索服务
     */
    filterSnippets(searchTerm) {
        const term = searchTerm.toLowerCase().trim();
        const cacheKey = `search_${term}`;

        // 检查缓存
        if (this.searchCache.has(cacheKey)) {
            console.log('🎯 使用搜索缓存:', term);
            this.filteredSnippets = this.searchCache.get(cacheKey);
            this.renderSnippetsList();
            return;
        }

        // 执行搜索
        let results;
        if (!term) {
            results = [...this.promptSnippets];
        } else {
            results = this.promptSnippets.filter(snippet =>
                snippet.name.toLowerCase().includes(term) ||
                snippet.channel.toLowerCase().includes(term) ||
                snippet.field.toLowerCase().includes(term) ||
                snippet.template.toLowerCase().includes(term) ||
                snippet.usage?.toLowerCase().includes(term)
            );
        }

        // 缓存结果
        this.searchCache.set(cacheKey, results);

        // 限制缓存大小
        if (this.searchCache.size > 50) {
            const firstKey = this.searchCache.keys().next().value;
            this.searchCache.delete(firstKey);
        }

        this.filteredSnippets = results;
        this.renderSnippetsList();

        console.log(`🔍 搜索完成: "${term}" -> ${results.length} 个结果`);
    }

    /**
     * 按分类过滤片段
     * @SERVICE 片段分类过滤服务
     */
    filterSnippetsByCategory(category) {
        if (category === 'all') {
            this.filteredSnippets = [...this.promptSnippets];
        } else {
            this.filteredSnippets = this.promptSnippets.filter(snippet =>
                snippet.category === category
            );
        }

        this.renderSnippetsList();
    }

    /**
     * 选择片段进行编辑
     * @SERVICE 片段选择服务
     */
    selectSnippet(snippetId) {
        // 更新列表中的激活状态
        document.querySelectorAll('.snippet-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-snippet-id="${snippetId}"]`).classList.add('active');

        // 查找片段数据
        this.currentSnippet = this.promptSnippets.find(s => s.id === snippetId);

        if (this.currentSnippet) {
            this.loadSnippetToForm(this.currentSnippet);
        }
    }

    /**
     * 将片段数据加载到表单
     * @SERVICE 表单数据加载服务
     */
    loadSnippetToForm(snippet) {
        document.getElementById('pe-editor-title').textContent = `编辑片段: ${snippet.name}`;
        document.getElementById('pe-snippet-name').value = snippet.name;
        document.getElementById('pe-snippet-channel').value = snippet.channel;
        document.getElementById('pe-snippet-field').value = snippet.field;
        document.getElementById('pe-snippet-category').value = snippet.category;
        document.getElementById('pe-snippet-usage').value = snippet.usage || '';
        document.getElementById('pe-snippet-template').value = snippet.template;

        // 启用删除按钮
        document.getElementById('pe-delete-btn').disabled = false;
    }

    /**
     * 创建新片段
     * @SERVICE 新片段创建服务
     */
    createNewSnippet() {
        // 清空表单
        this.resetForm();

        // 设置默认值
        document.getElementById('pe-editor-title').textContent = '新增片段';
        document.getElementById('pe-snippet-channel').value = 'generic';
        document.getElementById('pe-snippet-category').value = 'custom';

        // 清除列表选中状态
        document.querySelectorAll('.snippet-item').forEach(item => {
            item.classList.remove('active');
        });

        // 创建临时片段对象
        this.currentSnippet = {
            id: `new_${Date.now()}`,
            name: '',
            channel: 'generic',
            field: '',
            category: 'custom',
            usage: '',
            template: '',
            isNew: true
        };

        // 禁用删除按钮
        document.getElementById('pe-delete-btn').disabled = true;
    }

    /**
     * 重置表单
     * @SERVICE 表单重置服务
     */
    resetForm() {
        document.getElementById('pe-editor-title').textContent = '选择一个片段进行编辑';
        document.getElementById('pe-snippet-name').value = '';
        document.getElementById('pe-snippet-channel').value = '';
        document.getElementById('pe-snippet-field').value = '';
        document.getElementById('pe-snippet-category').value = 'extraction';
        document.getElementById('pe-snippet-usage').value = '';
        document.getElementById('pe-snippet-template').value = '';

        this.currentSnippet = null;
        document.getElementById('pe-delete-btn').disabled = true;
    }

    /**
     * 保存当前片段
     * @SERVICE 片段保存服务
     * @LIFECYCLE 数据生命周期管理
     * @DEPENDENCY 依赖 localStorageManager、promptComposer、表单验证
     *
     * 核心流程：
     * 1. 获取并验证表单数据
     * 2. 检查渠道/字段冲突
     * 3. 更新内存中的片段数据
     * 4. 持久化到 localStorage
     * 5. 清理相关缓存
     * 6. 更新界面显示
     * 7. 触发系统更新事件
     */
    async saveCurrentSnippet() {
        try {
            // 获取表单数据
            const formData = this.getFormData();

            // 验证表单
            const validation = this.validateForm(formData);
            if (!validation.valid) {
                this.showError(validation.message);
                return;
            }

            // 更新当前片段数据
            if (this.currentSnippet) {
                Object.assign(this.currentSnippet, formData);
                this.currentSnippet.updatedAt = new Date().toISOString();

                if (this.currentSnippet.isNew) {
                    // 新增片段
                    this.currentSnippet.id = `${formData.channel}_${formData.field}_${Date.now()}`;
                    this.currentSnippet.createdAt = new Date().toISOString();
                    delete this.currentSnippet.isNew;

                    // 添加到内存列表
                    this.promptSnippets.push(this.currentSnippet);
                    this.filteredSnippets.push(this.currentSnippet);
                }

                // 保存到存储
                await this.saveSnippetToStorage(this.currentSnippet);

                // 更新界面
                this.renderSnippetsList();
                this.selectSnippet(this.currentSnippet.id);

                this.showSuccess('片段保存成功！');
            }
        } catch (error) {
            console.error('保存片段失败:', error);
            this.showError('保存失败: ' + error.message);
        }
    }

    /**
     * 删除当前片段
     * @SERVICE 片段删除服务
     */
    async deleteCurrentSnippet() {
        if (!this.currentSnippet || this.currentSnippet.isNew) {
            return;
        }

        if (!confirm(`确定要删除片段"${this.currentSnippet.name}"吗？`)) {
            return;
        }

        try {
            // 从存储删除
            await this.deleteSnippetFromStorage(this.currentSnippet);

            // 从内存列表删除
            this.promptSnippets = this.promptSnippets.filter(s => s.id !== this.currentSnippet.id);
            this.filteredSnippets = this.filteredSnippets.filter(s => s.id !== this.currentSnippet.id);

            // 重置表单
            this.resetForm();

            // 更新界面
            this.renderSnippetsList();

            this.showSuccess('片段删除成功！');
        } catch (error) {
            console.error('删除片段失败:', error);
            this.showError('删除失败: ' + error.message);
        }
    }

    /**
     * 获取表单数据
     * @UTIL 表单数据获取工具
     */
    getFormData() {
        return {
            name: document.getElementById('pe-snippet-name').value.trim(),
            channel: document.getElementById('pe-snippet-channel').value.trim(),
            field: document.getElementById('pe-snippet-field').value.trim(),
            category: document.getElementById('pe-snippet-category').value,
            usage: document.getElementById('pe-snippet-usage').value.trim(),
            template: document.getElementById('pe-snippet-template').value.trim()
        };
    }

    /**
     * 验证表单数据
     * @UTIL 表单验证工具
     */
    validateForm(data) {
        if (!data.name) {
            return { valid: false, message: '请输入片段名称' };
        }

        if (!data.channel) {
            return { valid: false, message: '请输入所属渠道' };
        }

        if (!data.field) {
            return { valid: false, message: '请输入对应字段' };
        }

        if (!data.template) {
            return { valid: false, message: '请输入提示词模板' };
        }

        // 检查是否与现有片段冲突（除了当前编辑的片段）
        const existingSnippet = this.promptSnippets.find(s =>
            s.channel === data.channel &&
            s.field === data.field &&
            s.id !== (this.currentSnippet?.id)
        );

        if (existingSnippet) {
            return {
                valid: false,
                message: `渠道"${data.channel}"的字段"${data.field}"已存在片段，请使用不同的渠道/字段组合`
            };
        }

        return { valid: true };
    }

    /**
     * 显示成功消息
     * @UTIL 消息提示工具
     */
    showSuccess(message) {
        // 简单的成功提示实现
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: #28a745; color: white; padding: 12px 20px;
            border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.remove(), 3000);
    }

    /**
     * 显示错误消息
     * @UTIL 消息提示工具
     */
    showError(message) {
        // 简单的错误提示实现
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: #dc3545; color: white; padding: 12px 20px;
            border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.remove(), 5000);
    }

    /**
     * 分析当前片段（AI功能）
     * @SERVICE AI分析服务
     */
    async analyzeCurrentSnippet() {
        if (!this.currentSnippet) {
            this.showError('请先选择一个片段');
            return;
        }

        try {
            // 显示分析进度
            document.getElementById('pe-ai-content').innerHTML = `
                <div style="padding: 20px; text-align: center;">
                    <h4>🤖 正在分析片段...</h4>
                    <div style="margin: 20px 0;">
                        <div style="display: inline-block; width: 20px; height: 20px; border: 2px solid #6f42c1; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    </div>
                    <p>分析片段: ${this.currentSnippet.name}</p>
                </div>
                <style>
                    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
                </style>
            `;

            // 模拟订单内容进行分析
            const mockOrderContent = `订单编号：TEST123456
客户姓名：张三
联系电话：+60123456789
服务地址：吉隆坡国际机场KLIA2
预约时间：2024-01-15 14:30`;

            // 使用AI优化器分析
            const result = await this.aiOptimizer.optimizeFieldPrompt(
                this.currentSnippet.field,
                mockOrderContent,
                this.currentSnippet.template
            );

            // 显示分析结果
            this.showSingleOptimizationResult(result);

        } catch (error) {
            console.error('分析片段失败:', error);
            this.showError('分析失败: ' + error.message);
        }
    }

    /**
     * 显示单个优化结果
     * @SERVICE 单个结果显示服务
     */
    showSingleOptimizationResult(result) {
        document.getElementById('pe-ai-content').innerHTML = `
            <div style="padding: 20px;">
                <h4>🤖 分析结果</h4>
                <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <div style="margin-bottom: 10px;">
                        <strong>字段:</strong> ${result.field}
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>预期提升:</strong> <span style="color: #28a745;">+${result.expectedImprovement}%</span>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <strong>主要改进:</strong>
                        <ul style="margin: 5px 0; padding-left: 20px;">
                            ${result.improvements.map(imp => `<li style="font-size: 14px;">${imp}</li>`).join('')}
                        </ul>
                    </div>
                    ${result.reasoning ? `
                        <div style="margin-bottom: 15px;">
                            <strong>优化理由:</strong>
                            <p style="font-size: 14px; color: #666;">${result.reasoning}</p>
                        </div>
                    ` : ''}
                </div>

                <div style="display: flex; gap: 10px; margin-top: 20px;">
                    <button class="btn btn-primary btn-sm" onclick="window.promptEditor.showVersionComparison('${result.field}', \`${result.originalPrompt}\`, \`${result.optimizedPrompt}\`)">
                        查看对比
                    </button>
                    <button class="btn btn-success btn-sm" onclick="window.promptEditor.applyOptimization('${result.field}', \`${result.optimizedPrompt}\`)">
                        应用优化
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 应用优化结果
     * @SERVICE 优化应用服务
     */
    async applyOptimization(field, optimizedPrompt) {
        try {
            if (this.currentSnippet && this.currentSnippet.field === field) {
                // 更新当前片段
                this.currentSnippet.template = optimizedPrompt;
                this.currentSnippet.updatedAt = new Date().toISOString();

                // 更新表单
                document.getElementById('pe-snippet-template').value = optimizedPrompt;

                // 保存到存储
                await this.saveSnippetToStorage(this.currentSnippet);

                this.showSuccess('优化已应用并保存！');
            } else {
                this.showError('当前片段与优化结果不匹配');
            }
        } catch (error) {
            console.error('应用优化失败:', error);
            this.showError('应用优化失败: ' + error.message);
        }
    }

    /**
     * 显示版本对比界面
     * @SERVICE 版本对比服务
     * @param {string} field - 字段名
     * @param {string} originalPrompt - 原版提示词
     * @param {string} optimizedPrompt - 优化版提示词
     */
    showVersionComparison(field, originalPrompt, optimizedPrompt) {
        // 创建对比模态框
        const comparisonModal = document.createElement('div');
        comparisonModal.id = 'version-comparison-modal';
        comparisonModal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 10000;
        `;

        const comparisonContent = document.createElement('div');
        comparisonContent.style.cssText = `
            background: white; border-radius: 12px; width: 90%; max-width: 1200px;
            height: 80vh; max-height: 700px; display: flex; flex-direction: column;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        `;

        comparisonContent.innerHTML = this.getVersionComparisonHTML(field, originalPrompt, optimizedPrompt);
        comparisonModal.appendChild(comparisonContent);
        document.body.appendChild(comparisonModal);

        // 绑定事件
        this.bindComparisonEvents(comparisonModal, field, optimizedPrompt);

        // 点击遮罩关闭
        comparisonModal.addEventListener('click', (e) => {
            if (e.target === comparisonModal) {
                this.closeVersionComparison();
            }
        });

        // 执行差异分析
        this.performDiffAnalysis(originalPrompt, optimizedPrompt);
    }

    /**
     * 获取版本对比HTML
     * @UTIL 对比界面模板
     */
    getVersionComparisonHTML(field, originalPrompt, optimizedPrompt) {
        return `
            <div class="comparison-header" style="background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%); color: white; padding: 20px; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h3>📊 版本对比: ${field}</h3>
                <button id="vc-close-btn" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 5px 10px; border-radius: 4px;">×</button>
            </div>

            <div class="comparison-body" style="flex: 1; display: flex; flex-direction: column; padding: 20px;">
                <div class="comparison-controls" style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
                    <button class="btn btn-sm btn-secondary" id="vc-toggle-view" data-view="side-by-side">切换为统一视图</button>
                    <button class="btn btn-sm btn-secondary" id="vc-toggle-diff">显示/隐藏差异</button>
                    <div style="margin-left: auto; font-size: 14px; color: #666;">
                        <span id="vc-diff-stats">分析中...</span>
                    </div>
                </div>

                <div class="comparison-content" style="flex: 1; display: flex; gap: 20px; overflow: hidden;">
                    <!-- 并排对比视图 -->
                    <div id="side-by-side-view" class="comparison-view" style="display: flex; gap: 20px; width: 100%;">
                        <div class="version-panel" style="flex: 1; display: flex; flex-direction: column;">
                            <div class="version-header" style="background: #dc3545; color: white; padding: 10px; border-radius: 6px 6px 0 0; text-align: center; font-weight: 500;">
                                原版提示词
                            </div>
                            <div class="version-content" style="flex: 1; border: 1px solid #dc3545; border-top: none; border-radius: 0 0 6px 6px; overflow: hidden;">
                                <pre id="original-content" style="margin: 0; padding: 15px; height: 100%; overflow-y: auto; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 13px; line-height: 1.5; background: #fff5f5;">${this.escapeHtml(originalPrompt)}</pre>
                            </div>
                        </div>

                        <div class="version-panel" style="flex: 1; display: flex; flex-direction: column;">
                            <div class="version-header" style="background: #28a745; color: white; padding: 10px; border-radius: 6px 6px 0 0; text-align: center; font-weight: 500;">
                                优化版提示词
                            </div>
                            <div class="version-content" style="flex: 1; border: 1px solid #28a745; border-top: none; border-radius: 0 0 6px 6px; overflow: hidden;">
                                <pre id="optimized-content" style="margin: 0; padding: 15px; height: 100%; overflow-y: auto; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 13px; line-height: 1.5; background: #f0fff4;">${this.escapeHtml(optimizedPrompt)}</pre>
                            </div>
                        </div>
                    </div>

                    <!-- 统一对比视图 -->
                    <div id="unified-view" class="comparison-view" style="display: none; width: 100%;">
                        <div class="unified-content" style="height: 100%; border: 1px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                            <pre id="unified-diff" style="margin: 0; padding: 15px; height: 100%; overflow-y: auto; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 13px; line-height: 1.5; background: white;"></pre>
                        </div>
                    </div>
                </div>

                <div class="comparison-actions" style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                    <button class="btn btn-success" id="vc-apply-btn">✅ 应用优化版</button>
                    <button class="btn btn-secondary" id="vc-keep-original-btn">📝 保留原版</button>
                    <button class="btn btn-secondary" id="vc-manual-edit-btn">✏️ 手动编辑</button>
                </div>
            </div>
        `;
    }

    /**
     * HTML转义工具
     * @UTIL HTML转义工具
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 绑定对比界面事件
     * @EVENT_HANDLER 对比界面事件绑定
     */
    bindComparisonEvents(modal, field, optimizedPrompt) {
        // 关闭按钮
        modal.querySelector('#vc-close-btn').addEventListener('click', () => {
            this.closeVersionComparison();
        });

        // 视图切换
        modal.querySelector('#vc-toggle-view').addEventListener('click', (e) => {
            const currentView = e.target.dataset.view;
            if (currentView === 'side-by-side') {
                document.getElementById('side-by-side-view').style.display = 'none';
                document.getElementById('unified-view').style.display = 'block';
                e.target.textContent = '切换为并排视图';
                e.target.dataset.view = 'unified';
            } else {
                document.getElementById('side-by-side-view').style.display = 'flex';
                document.getElementById('unified-view').style.display = 'none';
                e.target.textContent = '切换为统一视图';
                e.target.dataset.view = 'side-by-side';
            }
        });

        // 应用优化
        modal.querySelector('#vc-apply-btn').addEventListener('click', async () => {
            try {
                await this.applyOptimization(field, optimizedPrompt);
                this.closeVersionComparison();
            } catch (error) {
                this.showError('应用优化失败: ' + error.message);
            }
        });

        // 保留原版
        modal.querySelector('#vc-keep-original-btn').addEventListener('click', () => {
            this.closeVersionComparison();
            this.showSuccess('已保留原版提示词');
        });

        // 手动编辑
        modal.querySelector('#vc-manual-edit-btn').addEventListener('click', () => {
            this.closeVersionComparison();
            // 聚焦到编辑器的模板输入框
            const templateInput = document.getElementById('pe-snippet-template');
            if (templateInput) {
                templateInput.focus();
                templateInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }

    /**
     * 关闭版本对比
     * @LIFECYCLE 对比界面生命周期
     */
    closeVersionComparison() {
        const modal = document.getElementById('version-comparison-modal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 执行差异分析
     * @SERVICE 差异分析服务
     */
    performDiffAnalysis(originalPrompt, optimizedPrompt) {
        try {
            const originalLines = originalPrompt.split('\n');
            const optimizedLines = optimizedPrompt.split('\n');

            // 简单的逐行差异分析
            const diffResult = this.calculateLineDiff(originalLines, optimizedLines);

            // 更新统计信息
            const statsElement = document.getElementById('vc-diff-stats');
            if (statsElement) {
                statsElement.textContent = `添加 ${diffResult.added} 行，删除 ${diffResult.removed} 行，修改 ${diffResult.modified} 行`;
            }

            // 生成统一差异视图
            this.generateUnifiedDiff(originalLines, optimizedLines, diffResult);

            // 高亮并排视图中的差异
            this.highlightSideBySideDiff(originalLines, optimizedLines, diffResult);

        } catch (error) {
            console.error('差异分析失败:', error);
            const statsElement = document.getElementById('vc-diff-stats');
            if (statsElement) {
                statsElement.textContent = '差异分析失败';
            }
        }
    }

    /**
     * 计算逐行差异
     * @UTIL 差异计算工具
     */
    calculateLineDiff(originalLines, optimizedLines) {
        const result = {
            added: 0,
            removed: 0,
            modified: 0,
            changes: []
        };

        const maxLines = Math.max(originalLines.length, optimizedLines.length);

        for (let i = 0; i < maxLines; i++) {
            const originalLine = originalLines[i] || '';
            const optimizedLine = optimizedLines[i] || '';

            if (originalLine === optimizedLine) {
                result.changes.push({ type: 'equal', original: originalLine, optimized: optimizedLine, lineNum: i });
            } else if (originalLine && !optimizedLine) {
                result.removed++;
                result.changes.push({ type: 'removed', original: originalLine, optimized: '', lineNum: i });
            } else if (!originalLine && optimizedLine) {
                result.added++;
                result.changes.push({ type: 'added', original: '', optimized: optimizedLine, lineNum: i });
            } else {
                result.modified++;
                result.changes.push({ type: 'modified', original: originalLine, optimized: optimizedLine, lineNum: i });
            }
        }

        return result;
    }

    /**
     * 生成统一差异视图
     * @SERVICE 统一差异生成服务
     */
    generateUnifiedDiff(originalLines, optimizedLines, diffResult) {
        const unifiedDiffElement = document.getElementById('unified-diff');
        if (!unifiedDiffElement) return;

        let unifiedContent = '';

        diffResult.changes.forEach((change, index) => {
            const lineNum = (index + 1).toString().padStart(3, ' ');

            switch (change.type) {
                case 'equal':
                    unifiedContent += `  ${lineNum} ${change.original}\n`;
                    break;
                case 'removed':
                    unifiedContent += `- ${lineNum} ${change.original}\n`;
                    break;
                case 'added':
                    unifiedContent += `+ ${lineNum} ${change.optimized}\n`;
                    break;
                case 'modified':
                    unifiedContent += `- ${lineNum} ${change.original}\n`;
                    unifiedContent += `+ ${lineNum} ${change.optimized}\n`;
                    break;
            }
        });

        unifiedDiffElement.textContent = unifiedContent;

        // 添加语法高亮样式
        this.applyDiffSyntaxHighlighting(unifiedDiffElement);
    }

    /**
     * 应用差异语法高亮
     * @UTIL 语法高亮工具
     */
    applyDiffSyntaxHighlighting(element) {
        const lines = element.textContent.split('\n');
        let highlightedContent = '';

        lines.forEach(line => {
            if (line.startsWith('- ')) {
                highlightedContent += `<span style="background: #ffebee; color: #c62828;">${this.escapeHtml(line)}</span>\n`;
            } else if (line.startsWith('+ ')) {
                highlightedContent += `<span style="background: #e8f5e8; color: #2e7d32;">${this.escapeHtml(line)}</span>\n`;
            } else {
                highlightedContent += `<span>${this.escapeHtml(line)}</span>\n`;
            }
        });

        element.innerHTML = highlightedContent;
    }

    /**
     * 高亮并排视图差异
     * @SERVICE 并排差异高亮服务
     */
    highlightSideBySideDiff(originalLines, optimizedLines, diffResult) {
        const originalElement = document.getElementById('original-content');
        const optimizedElement = document.getElementById('optimized-content');

        if (!originalElement || !optimizedElement) return;

        // 高亮原版内容
        let originalHighlighted = '';
        diffResult.changes.forEach(change => {
            if (change.type === 'removed' || change.type === 'modified') {
                originalHighlighted += `<span style="background: #ffebee; color: #c62828;">${this.escapeHtml(change.original)}</span>\n`;
            } else {
                originalHighlighted += `<span>${this.escapeHtml(change.original)}</span>\n`;
            }
        });
        originalElement.innerHTML = originalHighlighted;

        // 高亮优化版内容
        let optimizedHighlighted = '';
        diffResult.changes.forEach(change => {
            if (change.type === 'added' || change.type === 'modified') {
                optimizedHighlighted += `<span style="background: #e8f5e8; color: #2e7d32;">${this.escapeHtml(change.optimized)}</span>\n`;
            } else {
                optimizedHighlighted += `<span>${this.escapeHtml(change.optimized)}</span>\n`;
            }
        });
        optimizedElement.innerHTML = optimizedHighlighted;
    }

    /**
     * 导出所有数据
     * @SERVICE 数据导出服务
     * @FACTORY 数据工厂模式，生成导出数据结构
     * @DEPENDENCY 依赖 localStorageManager、aiOptimizer、统计服务
     *
     * 导出内容：
     * - 提示词片段数据（完整）
     * - 本地存储数据（规则、历史等）
     * - AI优化历史记录
     * - 统计信息和元数据
     * - 导出时间戳和版本信息
     */
    async exportAllData() {
        try {
            console.log('📤 开始导出数据...');

            // 收集所有数据
            const exportData = {
                // 基本信息
                exportInfo: {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    source: 'PromptEditor',
                    description: '提示词编辑器完整数据导出'
                },

                // 提示词片段数据
                promptSnippets: this.promptSnippets,

                // 本地存储数据
                localStorage: this.localStorageManager.exportData(),

                // AI优化历史
                optimizationHistory: this.aiOptimizer.optimizationHistory,

                // 统计信息
                statistics: {
                    snippets: this.getSnippetsStats(),
                    exportTime: new Date().toLocaleString('zh-CN')
                }
            };

            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const filename = `prompt-editor-export-${timestamp}.json`;

            // 创建并下载文件
            this.downloadJsonFile(exportData, filename);

            this.showSuccess(`数据导出成功！文件名: ${filename}`);

            console.log('✅ 数据导出完成:', exportData);
            return { success: true, filename, data: exportData };

        } catch (error) {
            console.error('❌ 数据导出失败:', error);
            this.showError('导出失败: ' + error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 导出选定的提示词片段
     * @SERVICE 选择性导出服务
     */
    async exportSelectedSnippets(snippetIds) {
        try {
            const selectedSnippets = this.promptSnippets.filter(snippet =>
                snippetIds.includes(snippet.id)
            );

            if (selectedSnippets.length === 0) {
                this.showError('没有选择要导出的片段');
                return { success: false, error: '没有选择片段' };
            }

            const exportData = {
                exportInfo: {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    type: 'selected_snippets',
                    count: selectedSnippets.length
                },
                promptSnippets: selectedSnippets,
                statistics: {
                    total: selectedSnippets.length,
                    byCategory: {},
                    byChannel: {}
                }
            };

            // 统计选中片段
            selectedSnippets.forEach(snippet => {
                exportData.statistics.byCategory[snippet.category] =
                    (exportData.statistics.byCategory[snippet.category] || 0) + 1;
                exportData.statistics.byChannel[snippet.channel] =
                    (exportData.statistics.byChannel[snippet.channel] || 0) + 1;
            });

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const filename = `prompt-snippets-${selectedSnippets.length}-${timestamp}.json`;

            this.downloadJsonFile(exportData, filename);
            this.showSuccess(`已导出 ${selectedSnippets.length} 个片段`);

            return { success: true, filename, data: exportData };

        } catch (error) {
            console.error('导出选定片段失败:', error);
            this.showError('导出失败: ' + error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 按渠道导出片段
     * @SERVICE 按渠道导出服务
     */
    async exportByChannel(channelId) {
        try {
            const channelSnippets = this.promptSnippets.filter(snippet =>
                snippet.channel === channelId
            );

            if (channelSnippets.length === 0) {
                this.showError(`渠道 "${channelId}" 没有片段可导出`);
                return { success: false, error: '没有片段' };
            }

            const exportData = {
                exportInfo: {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    type: 'channel_export',
                    channel: channelId,
                    count: channelSnippets.length
                },
                promptSnippets: channelSnippets,
                statistics: {
                    channel: channelId,
                    total: channelSnippets.length,
                    byCategory: {}
                }
            };

            // 统计分类
            channelSnippets.forEach(snippet => {
                exportData.statistics.byCategory[snippet.category] =
                    (exportData.statistics.byCategory[snippet.category] || 0) + 1;
            });

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const filename = `prompt-${channelId}-${timestamp}.json`;

            this.downloadJsonFile(exportData, filename);
            this.showSuccess(`已导出渠道 "${channelId}" 的 ${channelSnippets.length} 个片段`);

            return { success: true, filename, data: exportData };

        } catch (error) {
            console.error('按渠道导出失败:', error);
            this.showError('导出失败: ' + error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 下载JSON文件
     * @UTIL JSON文件下载工具
     */
    downloadJsonFile(data, filename) {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
    }

    /**
     * 显示导出选项对话框
     * @SERVICE 导出选项服务
     */
    showExportOptions() {
        const exportModal = document.createElement('div');
        exportModal.id = 'export-options-modal';
        exportModal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 10001;
        `;

        exportModal.innerHTML = `
            <div style="background: white; border-radius: 12px; width: 90%; max-width: 500px; padding: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>📤 导出数据</h3>
                    <button id="export-close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
                </div>

                <div style="margin-bottom: 20px;">
                    <p style="color: #666; margin-bottom: 15px;">选择要导出的数据类型：</p>

                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <button class="btn btn-primary" id="export-all-btn" style="text-align: left; padding: 15px;">
                            <strong>📦 完整导出</strong><br>
                            <small style="color: rgba(255,255,255,0.8);">包含所有提示词片段、优化历史、统计数据</small>
                        </button>

                        <button class="btn btn-secondary" id="export-snippets-btn" style="text-align: left; padding: 15px;">
                            <strong>📝 仅提示词片段</strong><br>
                            <small style="color: rgba(255,255,255,0.8);">只导出当前的 ${this.promptSnippets.length} 个提示词片段</small>
                        </button>

                        <button class="btn btn-secondary" id="export-by-channel-btn" style="text-align: left; padding: 15px;">
                            <strong>🏷️ 按渠道导出</strong><br>
                            <small style="color: rgba(255,255,255,0.8);">选择特定渠道的片段进行导出</small>
                        </button>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn btn-secondary" id="export-cancel-btn">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(exportModal);

        // 绑定事件
        exportModal.querySelector('#export-close-btn').addEventListener('click', () => {
            exportModal.remove();
        });

        exportModal.querySelector('#export-cancel-btn').addEventListener('click', () => {
            exportModal.remove();
        });

        exportModal.querySelector('#export-all-btn').addEventListener('click', async () => {
            exportModal.remove();
            await this.exportAllData();
        });

        exportModal.querySelector('#export-snippets-btn').addEventListener('click', async () => {
            exportModal.remove();
            const allSnippetIds = this.promptSnippets.map(s => s.id);
            await this.exportSelectedSnippets(allSnippetIds);
        });

        exportModal.querySelector('#export-by-channel-btn').addEventListener('click', () => {
            exportModal.remove();
            this.showChannelExportDialog();
        });

        // 点击遮罩关闭
        exportModal.addEventListener('click', (e) => {
            if (e.target === exportModal) {
                exportModal.remove();
            }
        });
    }

    /**
     * 显示按渠道导出对话框
     * @SERVICE 渠道导出选择服务
     */
    showChannelExportDialog() {
        // 获取所有渠道
        const channels = [...new Set(this.promptSnippets.map(s => s.channel))];

        if (channels.length === 0) {
            this.showError('没有可导出的渠道');
            return;
        }

        const channelModal = document.createElement('div');
        channelModal.id = 'channel-export-modal';
        channelModal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 10001;
        `;

        channelModal.innerHTML = `
            <div style="background: white; border-radius: 12px; width: 90%; max-width: 400px; padding: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>🏷️ 选择渠道</h3>
                    <button id="channel-close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
                </div>

                <div style="margin-bottom: 20px;">
                    <p style="color: #666; margin-bottom: 15px;">选择要导出的渠道：</p>

                    <div style="max-height: 300px; overflow-y: auto;">
                        ${channels.map(channel => {
                            const count = this.promptSnippets.filter(s => s.channel === channel).length;
                            return `
                                <button class="btn btn-secondary channel-export-item" data-channel="${channel}"
                                        style="width: 100%; text-align: left; margin-bottom: 8px; padding: 12px;">
                                    <strong>${channel}</strong><br>
                                    <small style="color: #666;">${count} 个片段</small>
                                </button>
                            `;
                        }).join('')}
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn btn-secondary" id="channel-cancel-btn">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(channelModal);

        // 绑定事件
        channelModal.querySelector('#channel-close-btn').addEventListener('click', () => {
            channelModal.remove();
        });

        channelModal.querySelector('#channel-cancel-btn').addEventListener('click', () => {
            channelModal.remove();
        });

        channelModal.querySelectorAll('.channel-export-item').forEach(btn => {
            btn.addEventListener('click', async () => {
                const channel = btn.dataset.channel;
                channelModal.remove();
                await this.exportByChannel(channel);
            });
        });

        // 点击遮罩关闭
        channelModal.addEventListener('click', (e) => {
            if (e.target === channelModal) {
                channelModal.remove();
            }
        });
    }

    /**
     * 显示导入对话框
     * @SERVICE 数据导入服务
     */
    showImportDialog() {
        const importModal = document.createElement('div');
        importModal.id = 'import-dialog-modal';
        importModal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 10001;
        `;

        importModal.innerHTML = `
            <div style="background: white; border-radius: 12px; width: 90%; max-width: 600px; padding: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>📥 导入数据</h3>
                    <button id="import-close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
                </div>

                <div style="margin-bottom: 20px;">
                    <div style="border: 2px dashed #ddd; border-radius: 8px; padding: 40px; text-align: center; margin-bottom: 20px;" id="import-drop-zone">
                        <div style="font-size: 48px; color: #ddd; margin-bottom: 10px;">📁</div>
                        <p style="color: #666; margin-bottom: 10px;">拖拽JSON文件到此处，或点击选择文件</p>
                        <input type="file" id="import-file-input" accept=".json" style="display: none;">
                        <button class="btn btn-primary" id="import-select-btn">选择文件</button>
                    </div>

                    <div id="import-file-info" style="display: none; padding: 15px; background: #f8f9fa; border-radius: 6px; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong id="import-filename">文件名</strong><br>
                                <small id="import-filesize" style="color: #666;">文件大小</small>
                            </div>
                            <button id="import-remove-file" style="background: none; border: none; color: #dc3545; cursor: pointer;">✕</button>
                        </div>
                    </div>

                    <div id="import-preview" style="display: none;">
                        <h4>📋 导入预览</h4>
                        <div id="import-preview-content" style="max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; background: #f8f9fa; font-family: monospace; font-size: 12px;"></div>

                        <div style="margin: 15px 0;">
                            <h5>冲突处理策略</h5>
                            <div style="display: flex; gap: 10px; margin-top: 10px;">
                                <label style="display: flex; align-items: center; gap: 5px;">
                                    <input type="radio" name="conflict-strategy" value="overwrite" checked>
                                    <span>覆盖现有</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 5px;">
                                    <input type="radio" name="conflict-strategy" value="skip">
                                    <span>跳过冲突</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 5px;">
                                    <input type="radio" name="conflict-strategy" value="rename">
                                    <span>重命名导入</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button class="btn btn-primary" id="import-confirm-btn" style="display: none;">确认导入</button>
                    <button class="btn btn-secondary" id="import-cancel-btn">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(importModal);
        this.bindImportEvents(importModal);
    }

    /**
     * 绑定导入对话框事件
     * @EVENT_HANDLER 导入事件绑定
     */
    bindImportEvents(modal) {
        const fileInput = modal.querySelector('#import-file-input');
        const dropZone = modal.querySelector('#import-drop-zone');
        const selectBtn = modal.querySelector('#import-select-btn');
        const fileInfo = modal.querySelector('#import-file-info');
        const preview = modal.querySelector('#import-preview');

        let selectedFile = null;
        let importData = null;

        // 关闭按钮
        modal.querySelector('#import-close-btn').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('#import-cancel-btn').addEventListener('click', () => {
            modal.remove();
        });

        // 文件选择
        selectBtn.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleImportFile(e.target.files[0], modal);
            }
        });

        // 拖拽上传
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#6f42c1';
            dropZone.style.backgroundColor = '#f8f6ff';
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#ddd';
            dropZone.style.backgroundColor = 'transparent';
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#ddd';
            dropZone.style.backgroundColor = 'transparent';

            if (e.dataTransfer.files.length > 0) {
                this.handleImportFile(e.dataTransfer.files[0], modal);
            }
        });

        // 移除文件
        modal.querySelector('#import-remove-file').addEventListener('click', () => {
            selectedFile = null;
            importData = null;
            fileInfo.style.display = 'none';
            preview.style.display = 'none';
            modal.querySelector('#import-confirm-btn').style.display = 'none';
            fileInput.value = '';
        });

        // 确认导入
        modal.querySelector('#import-confirm-btn').addEventListener('click', async () => {
            if (importData) {
                const strategy = modal.querySelector('input[name="conflict-strategy"]:checked').value;
                modal.remove();
                await this.performImport(importData, strategy);
            }
        });

        // 点击遮罩关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * 处理导入文件
     * @SERVICE 文件处理服务
     */
    async handleImportFile(file, modal) {
        try {
            if (!file.name.endsWith('.json')) {
                this.showError('请选择JSON格式的文件');
                return;
            }

            // 显示文件信息
            modal.querySelector('#import-filename').textContent = file.name;
            modal.querySelector('#import-filesize').textContent = this.formatFileSize(file.size);
            modal.querySelector('#import-file-info').style.display = 'block';

            // 读取文件内容
            const content = await this.readFileAsText(file);
            const data = JSON.parse(content);

            // 验证数据格式
            const validation = this.validateImportData(data);
            if (!validation.valid) {
                this.showError('文件格式无效: ' + validation.error);
                return;
            }

            // 显示预览
            this.showImportPreview(data, modal);

            // 保存数据供导入使用
            window.importData = data;
            modal.querySelector('#import-confirm-btn').style.display = 'inline-block';

        } catch (error) {
            console.error('处理导入文件失败:', error);
            this.showError('文件读取失败: ' + error.message);
        }
    }

    /**
     * 格式化文件大小
     * @UTIL 文件大小格式化工具
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 读取文件为文本
     * @UTIL 文件读取工具
     */
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    /**
     * 验证导入数据格式
     * @UTIL 数据验证工具
     */
    validateImportData(data) {
        try {
            // 检查基本结构
            if (!data || typeof data !== 'object') {
                return { valid: false, error: '数据格式无效' };
            }

            // 检查是否包含提示词片段
            if (!data.promptSnippets && !data.localStorage?.prompts) {
                return { valid: false, error: '文件中未找到提示词数据' };
            }

            // 验证提示词片段格式
            if (data.promptSnippets) {
                if (!Array.isArray(data.promptSnippets)) {
                    return { valid: false, error: '提示词片段格式无效' };
                }

                for (const snippet of data.promptSnippets) {
                    if (!snippet.id || !snippet.name || !snippet.template) {
                        return { valid: false, error: '提示词片段缺少必要字段' };
                    }
                }
            }

            return { valid: true };

        } catch (error) {
            return { valid: false, error: '数据解析失败: ' + error.message };
        }
    }

    /**
     * 显示导入预览
     * @SERVICE 导入预览服务
     */
    showImportPreview(data, modal) {
        const previewContent = modal.querySelector('#import-preview-content');
        const preview = modal.querySelector('#import-preview');

        let snippetsCount = 0;
        let channelsCount = 0;
        let conflictsCount = 0;

        // 统计导入数据
        if (data.promptSnippets) {
            snippetsCount = data.promptSnippets.length;
            const channels = new Set(data.promptSnippets.map(s => s.channel));
            channelsCount = channels.size;

            // 检查冲突
            data.promptSnippets.forEach(snippet => {
                const existing = this.promptSnippets.find(s =>
                    s.channel === snippet.channel && s.field === snippet.field
                );
                if (existing) {
                    conflictsCount++;
                }
            });
        }

        const previewText = `
导入数据概览:
- 提示词片段: ${snippetsCount} 个
- 涉及渠道: ${channelsCount} 个
- 潜在冲突: ${conflictsCount} 个
- 导出时间: ${data.exportInfo?.timestamp || '未知'}
- 数据版本: ${data.exportInfo?.version || '未知'}

${conflictsCount > 0 ? `
⚠️ 检测到 ${conflictsCount} 个冲突项目
建议选择合适的冲突处理策略
` : '✅ 未检测到冲突，可以安全导入'}
        `.trim();

        previewContent.textContent = previewText;
        preview.style.display = 'block';
    }

    /**
     * 执行导入操作
     * @SERVICE 导入执行服务
     */
    async performImport(data, strategy) {
        try {
            console.log('📥 开始导入数据...', { strategy });

            let importedCount = 0;
            let skippedCount = 0;
            let conflictCount = 0;

            if (data.promptSnippets && Array.isArray(data.promptSnippets)) {
                for (const snippet of data.promptSnippets) {
                    try {
                        const result = await this.importSingleSnippet(snippet, strategy);

                        if (result.imported) {
                            importedCount++;
                        } else if (result.skipped) {
                            skippedCount++;
                        } else if (result.conflict) {
                            conflictCount++;
                        }

                    } catch (error) {
                        console.error('导入片段失败:', snippet.name, error);
                        skippedCount++;
                    }
                }
            }

            // 刷新界面数据
            this.loadAllSnippetsFromStorage();
            this.filteredSnippets = [...this.promptSnippets];
            this.renderSnippetsList();

            // 显示导入结果
            this.showImportResult(importedCount, skippedCount, conflictCount);

            console.log('✅ 数据导入完成');

        } catch (error) {
            console.error('❌ 数据导入失败:', error);
            this.showError('导入失败: ' + error.message);
        }
    }

    /**
     * 导入单个片段
     * @SERVICE 单片段导入服务
     */
    async importSingleSnippet(snippet, strategy) {
        // 检查是否存在冲突
        const existingSnippet = this.promptSnippets.find(s =>
            s.channel === snippet.channel && s.field === snippet.field
        );

        if (existingSnippet) {
            switch (strategy) {
                case 'skip':
                    return { skipped: true };

                case 'rename':
                    // 重命名导入
                    snippet.name = `${snippet.name} (导入)`;
                    snippet.field = `${snippet.field}_imported`;
                    break;

                case 'overwrite':
                default:
                    // 覆盖现有片段
                    break;
            }
        }

        // 确保片段有必要的字段
        const importSnippet = {
            id: snippet.id || `imported_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: snippet.name,
            template: snippet.template,
            category: snippet.category || 'custom',
            usage: snippet.usage || snippet.field,
            channel: snippet.channel || 'generic',
            field: snippet.field || 'unknown',
            createdAt: snippet.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // 保存到存储
        await this.saveSnippetToStorage(importSnippet);

        // 添加到内存列表（如果不存在）
        const existingIndex = this.promptSnippets.findIndex(s => s.id === importSnippet.id);
        if (existingIndex >= 0) {
            this.promptSnippets[existingIndex] = importSnippet;
        } else {
            this.promptSnippets.push(importSnippet);
        }

        return { imported: true };
    }

    /**
     * 显示导入结果
     * @SERVICE 导入结果显示服务
     */
    showImportResult(imported, skipped, conflicts) {
        const resultModal = document.createElement('div');
        resultModal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 10002;
        `;

        resultModal.innerHTML = `
            <div style="background: white; border-radius: 12px; width: 90%; max-width: 400px; padding: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2); text-align: center;">
                <div style="font-size: 48px; margin-bottom: 20px;">🎉</div>
                <h3 style="margin-bottom: 20px;">导入完成</h3>

                <div style="text-align: left; margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>成功导入:</span>
                        <span style="color: #28a745; font-weight: bold;">${imported} 个</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>跳过:</span>
                        <span style="color: #ffc107; font-weight: bold;">${skipped} 个</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>冲突处理:</span>
                        <span style="color: #17a2b8; font-weight: bold;">${conflicts} 个</span>
                    </div>
                </div>

                <button class="btn btn-primary" onclick="this.parentElement.parentElement.remove()">确定</button>
            </div>
        `;

        document.body.appendChild(resultModal);

        // 3秒后自动关闭
        setTimeout(() => {
            if (resultModal.parentElement) {
                resultModal.remove();
            }
        }, 5000);
    }

    /**
     * 触发系统更新
     * @SERVICE 系统更新通知服务
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     */
    triggerSystemUpdate(eventType, data) {
        try {
            console.log('🔄 触发系统更新:', eventType, data?.name || data?.id);

            // 1. 清理提示词组合器缓存
            this.promptComposer.clearCache();
            console.log('✅ 提示词组合器缓存已清理');

            // 2. 发送自定义事件通知其他模块
            const updateEvent = new CustomEvent('promptsUpdated', {
                detail: {
                    type: eventType,
                    data: data,
                    timestamp: new Date().toISOString(),
                    source: 'PromptEditor'
                }
            });
            window.dispatchEvent(updateEvent);
            console.log('📡 已发送 promptsUpdated 事件');

            // 3. 更新主界面显示（如果存在）
            this.updateMainInterfaceDisplay();

            // 4. 刷新当前编辑器状态
            this.refreshEditorState();

            // 5. 记录更新历史
            this.recordSystemUpdate(eventType, data);

        } catch (error) {
            console.error('❌ 系统更新失败:', error);
        }
    }

    /**
     * 更新主界面显示
     * @SERVICE 主界面更新服务
     */
    updateMainInterfaceDisplay() {
        try {
            // 查找主界面中可能的提示词状态显示区域
            const statusElements = [
                document.getElementById('prompt-status'),
                document.querySelector('.prompt-status'),
                document.querySelector('[data-prompt-status]')
            ].filter(el => el);

            if (statusElements.length > 0) {
                const stats = this.getSnippetsStats();
                const statusText = `提示词片段: ${stats.total} 个 (${stats.customCount} 个自定义)`;

                statusElements.forEach(el => {
                    if (el.textContent !== undefined) {
                        el.textContent = statusText;

                        // 添加点击事件显示详细信息
                        el.style.cursor = 'pointer';
                        el.title = '点击查看详细信息';

                        // 移除之前的事件监听器（如果有）
                        el.removeEventListener('click', this.showDetailedUsageInfo);
                        el.addEventListener('click', () => this.showDetailedUsageInfo());
                    }
                });

                console.log('✅ 主界面状态已更新:', statusText);
            }

            // 更新按钮状态或其他UI元素
            const editButton = document.getElementById('editPromptBtn');
            if (editButton) {
                // 可以添加一个小的视觉反馈，表示数据已更新
                editButton.style.boxShadow = '0 0 10px rgba(111, 66, 193, 0.3)';
                setTimeout(() => {
                    editButton.style.boxShadow = '';
                }, 1000);
            }

        } catch (error) {
            console.warn('⚠️ 更新主界面显示失败:', error);
        }
    }

    /**
     * 刷新编辑器状态
     * @SERVICE 编辑器状态刷新服务
     */
    refreshEditorState() {
        try {
            // 如果编辑器当前是打开的，刷新数据
            const editorModal = document.getElementById('prompt-editor-modal');
            if (editorModal) {
                // 重新渲染片段列表
                this.renderSnippetsList();

                // 更新统计信息
                const stats = this.getSnippetsStats();
                console.log('📊 编辑器状态已刷新:', stats);
            }

        } catch (error) {
            console.warn('⚠️ 刷新编辑器状态失败:', error);
        }
    }

    /**
     * 记录系统更新历史
     * @SERVICE 更新历史记录服务
     */
    recordSystemUpdate(eventType, data) {
        try {
            if (!this.systemUpdateHistory) {
                this.systemUpdateHistory = [];
            }

            this.systemUpdateHistory.push({
                type: eventType,
                data: {
                    id: data?.id,
                    name: data?.name,
                    channel: data?.channel,
                    field: data?.field
                },
                timestamp: new Date().toISOString()
            });

            // 保持最近50条记录
            if (this.systemUpdateHistory.length > 50) {
                this.systemUpdateHistory = this.systemUpdateHistory.slice(-50);
            }

            console.log('📝 系统更新历史已记录');

        } catch (error) {
            console.warn('⚠️ 记录更新历史失败:', error);
        }
    }

    /**
     * 获取系统更新历史
     * @SERVICE 更新历史查询服务
     */
    getSystemUpdateHistory(limit = 10) {
        if (!this.systemUpdateHistory) {
            return [];
        }
        return this.systemUpdateHistory.slice(-limit).reverse();
    }

    /**
     * 强制刷新所有相关系统
     * @SERVICE 强制刷新服务
     */
    forceSystemRefresh() {
        try {
            console.log('🔄 强制刷新所有系统...');

            // 重新加载片段数据
            this.loadAllSnippetsFromStorage();

            // 清理所有缓存
            this.promptComposer.clearCache();

            // 触发全局刷新事件
            this.triggerSystemUpdate('force_refresh', { source: 'manual' });

            // 如果编辑器打开，刷新界面
            const editorModal = document.getElementById('prompt-editor-modal');
            if (editorModal) {
                this.filteredSnippets = [...this.promptSnippets];
                this.renderSnippetsList();
            }

            console.log('✅ 系统强制刷新完成');
            this.showSuccess('系统已刷新');

        } catch (error) {
            console.error('❌ 强制刷新失败:', error);
            this.showError('刷新失败: ' + error.message);
        }
    }

    /**
     * 初始化主界面集成
     * @SERVICE 主界面集成初始化服务
     * @INIT 系统初始化阶段执行
     * @EVENT_HANDLER 绑定全局事件监听器
     * @COMPONENT 增强现有UI组件功能
     *
     * 集成功能：
     * 1. 绑定快速编辑按钮
     * 2. 初始化状态显示区域
     * 3. 设置全局事件监听
     * 4. 增强字段卡片交互
     * 5. 添加实时更新通知
     */
    initializeMainInterfaceIntegration() {
        try {
            console.log('🔗 初始化主界面集成...');

            // 1. 绑定快速编辑按钮
            this.bindQuickEditButton();

            // 2. 初始化状态显示
            this.updateMainInterfaceDisplay();

            // 3. 监听全局事件
            this.setupGlobalEventListeners();

            // 4. 添加字段卡片的快速编辑功能
            this.enhanceFieldCards();

            console.log('✅ 主界面集成初始化完成');

        } catch (error) {
            console.error('❌ 主界面集成初始化失败:', error);
        }
    }

    /**
     * 绑定快速编辑按钮
     * @EVENT_HANDLER 快速编辑按钮事件绑定
     */
    bindQuickEditButton() {
        const quickEditBtn = document.getElementById('quick-edit-prompts');
        if (quickEditBtn) {
            quickEditBtn.addEventListener('click', () => {
                console.log('⚡ 快速编辑按钮点击');
                this.openEditor();
            });
            console.log('✅ 快速编辑按钮已绑定');
        }
    }

    /**
     * 设置全局事件监听器
     * @EVENT_HANDLER 全局事件监听设置
     */
    setupGlobalEventListeners() {
        // 监听提示词更新事件
        window.addEventListener('promptsUpdated', (event) => {
            console.log('📡 收到提示词更新事件:', event.detail);
            this.handlePromptsUpdatedEvent(event.detail);
        });

        // 监听字段处理完成事件（如果存在）
        window.addEventListener('fieldProcessingComplete', (event) => {
            console.log('📊 收到字段处理完成事件:', event.detail);
            this.highlightActiveFields(event.detail.processedFields);
        });

        console.log('✅ 全局事件监听器已设置');
    }

    /**
     * 增强字段卡片功能
     * @SERVICE 字段卡片增强服务
     */
    enhanceFieldCards() {
        const fieldCards = document.querySelectorAll('.field-card');

        fieldCards.forEach(card => {
            const fieldName = card.querySelector('h4')?.textContent;
            if (fieldName) {
                // 添加快速编辑图标
                this.addQuickEditIcon(card, fieldName);

                // 添加悬停效果
                this.addFieldCardHoverEffects(card, fieldName);
            }
        });

        console.log(`✅ 已增强 ${fieldCards.length} 个字段卡片`);
    }

    /**
     * 添加快速编辑图标到字段卡片
     * @SERVICE 快速编辑图标添加服务
     */
    addQuickEditIcon(card, fieldName) {
        // 检查是否已经添加过图标
        if (card.querySelector('.quick-edit-icon')) {
            return;
        }

        const header = card.querySelector('h4');
        if (header) {
            const editIcon = document.createElement('span');
            editIcon.className = 'quick-edit-icon';
            editIcon.innerHTML = '✏️';
            editIcon.style.cssText = `
                margin-left: 8px;
                cursor: pointer;
                opacity: 0.6;
                font-size: 12px;
                transition: opacity 0.2s;
            `;
            editIcon.title = `编辑 ${fieldName} 的提示词`;

            editIcon.addEventListener('click', (e) => {
                e.stopPropagation();
                this.quickEditField(fieldName);
            });

            editIcon.addEventListener('mouseenter', () => {
                editIcon.style.opacity = '1';
            });

            editIcon.addEventListener('mouseleave', () => {
                editIcon.style.opacity = '0.6';
            });

            header.appendChild(editIcon);
        }
    }

    /**
     * 添加字段卡片悬停效果
     * @SERVICE 字段卡片悬停效果服务
     */
    addFieldCardHoverEffects(card, fieldName) {
        const originalBorder = card.style.border || '';

        card.addEventListener('mouseenter', () => {
            card.style.border = '2px solid #6f42c1';
            card.style.boxShadow = '0 4px 12px rgba(111, 66, 193, 0.2)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.border = originalBorder;
            card.style.boxShadow = '';
        });
    }

    /**
     * 快速编辑字段
     * @SERVICE 字段快速编辑服务
     */
    quickEditField(fieldName) {
        console.log('⚡ 快速编辑字段:', fieldName);

        // 查找对应的提示词片段
        const relatedSnippets = this.promptSnippets.filter(snippet =>
            snippet.field === fieldName ||
            snippet.field.includes(fieldName) ||
            fieldName.includes(snippet.field) ||
            snippet.name.toLowerCase().includes(fieldName.toLowerCase())
        );

        if (relatedSnippets.length === 0) {
            // 没有找到相关片段，创建新的
            this.createQuickSnippetForField(fieldName);
        } else if (relatedSnippets.length === 1) {
            // 找到一个相关片段，直接编辑
            this.openEditorWithSnippet(relatedSnippets[0]);
        } else {
            // 找到多个相关片段，显示选择对话框
            this.showSnippetSelectionDialog(fieldName, relatedSnippets);
        }
    }

    /**
     * 为字段创建快速片段
     * @SERVICE 快速片段创建服务
     */
    createQuickSnippetForField(fieldName) {
        this.openEditor();

        // 等待编辑器打开后设置默认值
        setTimeout(() => {
            this.createNewSnippet();

            // 设置字段相关的默认值
            setTimeout(() => {
                document.getElementById('pe-snippet-name').value = `${fieldName} 提取`;
                document.getElementById('pe-snippet-field').value = fieldName;
                document.getElementById('pe-snippet-channel').value = 'generic';
                document.getElementById('pe-snippet-category').value = 'extraction';
                document.getElementById('pe-snippet-usage').value = `提取 ${fieldName} 字段信息`;
                document.getElementById('pe-snippet-template').value = `请从以下文本中准确提取 ${fieldName} 信息：\n\n{input}\n\n请返回提取到的 ${fieldName} 信息，如果未找到则返回空值。`;

                // 聚焦到模板输入框
                document.getElementById('pe-snippet-template').focus();
            }, 100);
        }, 500);
    }

    /**
     * 打开编辑器并选中特定片段
     * @SERVICE 编辑器片段选中服务
     */
    openEditorWithSnippet(snippet) {
        this.openEditor();

        // 等待编辑器打开后选中片段
        setTimeout(() => {
            this.selectSnippet(snippet.id);
        }, 500);
    }

    /**
     * 显示片段选择对话框
     * @SERVICE 片段选择对话框服务
     */
    showSnippetSelectionDialog(fieldName, snippets) {
        const selectionModal = document.createElement('div');
        selectionModal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 10001;
        `;

        selectionModal.innerHTML = `
            <div style="background: white; border-radius: 12px; width: 90%; max-width: 500px; padding: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>选择要编辑的片段</h3>
                    <button id="selection-close-btn" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
                </div>

                <p style="color: #666; margin-bottom: 20px;">找到 ${snippets.length} 个与 "${fieldName}" 相关的片段：</p>

                <div style="max-height: 300px; overflow-y: auto;">
                    ${snippets.map(snippet => `
                        <div class="snippet-selection-item" data-snippet-id="${snippet.id}"
                             style="padding: 15px; margin-bottom: 10px; border: 1px solid #e9ecef; border-radius: 6px; cursor: pointer; transition: all 0.2s;">
                            <div style="font-weight: 500; margin-bottom: 5px;">${snippet.name}</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                ${snippet.channel}/${snippet.field} • ${this.getCategoryLabel(snippet.category)}
                            </div>
                            <div style="font-size: 12px; color: #888; max-height: 40px; overflow: hidden;">
                                ${snippet.template.substring(0, 100)}${snippet.template.length > 100 ? '...' : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-secondary" id="selection-cancel-btn">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(selectionModal);

        // 绑定事件
        selectionModal.querySelector('#selection-close-btn').addEventListener('click', () => {
            selectionModal.remove();
        });

        selectionModal.querySelector('#selection-cancel-btn').addEventListener('click', () => {
            selectionModal.remove();
        });

        selectionModal.querySelectorAll('.snippet-selection-item').forEach(item => {
            item.addEventListener('mouseenter', () => {
                item.style.borderColor = '#6f42c1';
                item.style.backgroundColor = '#f8f6ff';
            });

            item.addEventListener('mouseleave', () => {
                item.style.borderColor = '#e9ecef';
                item.style.backgroundColor = 'white';
            });

            item.addEventListener('click', () => {
                const snippetId = item.dataset.snippetId;
                const snippet = snippets.find(s => s.id === snippetId);
                selectionModal.remove();
                this.openEditorWithSnippet(snippet);
            });
        });

        // 点击遮罩关闭
        selectionModal.addEventListener('click', (e) => {
            if (e.target === selectionModal) {
                selectionModal.remove();
            }
        });
    }

    /**
     * 处理提示词更新事件
     * @EVENT_HANDLER 提示词更新事件处理
     */
    handlePromptsUpdatedEvent(eventDetail) {
        try {
            console.log('🔄 处理提示词更新事件:', eventDetail.type);

            // 更新主界面状态显示
            this.updateMainInterfaceDisplay();

            // 如果有特定字段更新，高亮显示
            if (eventDetail.data && eventDetail.data.field) {
                this.highlightFieldCard(eventDetail.data.field, eventDetail.type);
            }

            // 显示更新通知
            if (eventDetail.type === 'snippet_saved') {
                this.showFieldUpdateNotification(eventDetail.data, '已保存');
            } else if (eventDetail.type === 'snippet_deleted') {
                this.showFieldUpdateNotification(eventDetail.data, '已删除');
            }

        } catch (error) {
            console.error('处理提示词更新事件失败:', error);
        }
    }

    /**
     * 高亮字段卡片
     * @SERVICE 字段卡片高亮服务
     */
    highlightFieldCard(fieldName, eventType) {
        const fieldCards = document.querySelectorAll('.field-card');

        fieldCards.forEach(card => {
            const cardFieldName = card.querySelector('h4')?.textContent;
            if (cardFieldName && (cardFieldName === fieldName || cardFieldName.includes(fieldName))) {
                // 添加高亮效果
                const highlightColor = eventType === 'snippet_saved' ? '#28a745' :
                                     eventType === 'snippet_deleted' ? '#dc3545' : '#6f42c1';

                card.style.transition = 'all 0.3s ease';
                card.style.borderColor = highlightColor;
                card.style.boxShadow = `0 0 15px ${highlightColor}33`;

                // 2秒后移除高亮
                setTimeout(() => {
                    card.style.borderColor = '';
                    card.style.boxShadow = '';
                }, 2000);
            }
        });
    }

    /**
     * 高亮活跃字段
     * @SERVICE 活跃字段高亮服务
     */
    highlightActiveFields(processedFields) {
        if (!processedFields || !Array.isArray(processedFields)) {
            return;
        }

        console.log('🎯 高亮活跃字段:', processedFields);

        processedFields.forEach(fieldName => {
            this.highlightFieldCard(fieldName, 'field_processed');
        });
    }

    /**
     * 显示字段更新通知
     * @SERVICE 字段更新通知服务
     */
    showFieldUpdateNotification(data, action) {
        if (!data || !data.name) return;

        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 80px; right: 20px; z-index: 10000;
            background: white; border: 1px solid #e9ecef; border-radius: 8px;
            padding: 15px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 300px; animation: slideInRight 0.3s ease;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <div style="font-size: 20px;">
                    ${action === '已保存' ? '💾' : action === '已删除' ? '🗑️' : '📝'}
                </div>
                <div>
                    <div style="font-weight: 500; margin-bottom: 2px;">
                        ${data.name} ${action}
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        ${data.channel}/${data.field}
                    </div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="background: none; border: none; font-size: 16px; cursor: pointer; margin-left: auto;">×</button>
            </div>
        `;

        // 添加动画样式
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    /**
     * 获取当前使用的片段信息
     * @SERVICE 当前片段信息获取服务
     */
    getCurrentUsageInfo() {
        const stats = this.getSnippetsStats();
        const recentlyUsed = this.getRecentlyUsedSnippets(5);

        return {
            total: stats.total,
            custom: stats.customCount,
            default: stats.defaultCount,
            byCategory: stats.byCategory,
            byChannel: stats.byChannel,
            recentlyUsed: recentlyUsed,
            lastUpdated: new Date().toISOString()
        };
    }

    /**
     * 获取最近使用的片段
     * @SERVICE 最近使用片段获取服务
     */
    getRecentlyUsedSnippets(limit = 5) {
        return this.promptSnippets
            .filter(snippet => snippet.updatedAt)
            .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
            .slice(0, limit)
            .map(snippet => ({
                id: snippet.id,
                name: snippet.name,
                channel: snippet.channel,
                field: snippet.field,
                updatedAt: snippet.updatedAt
            }));
    }

    /**
     * 显示详细的片段使用信息
     * @SERVICE 详细信息显示服务
     */
    showDetailedUsageInfo() {
        const info = this.getCurrentUsageInfo();
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center;
            align-items: center; z-index: 10001;
        `;

        modal.innerHTML = `
            <div style="background: white; border-radius: 12px; width: 90%; max-width: 600px; padding: 30px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>📊 提示词使用详情</h3>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()"
                            style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4>📈 统计概览</h4>
                        <div style="margin-top: 10px;">
                            <div>总片段: <strong>${info.total}</strong></div>
                            <div>自定义: <strong>${info.custom}</strong></div>
                            <div>默认: <strong>${info.default}</strong></div>
                        </div>
                    </div>

                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4>🏷️ 分类分布</h4>
                        <div style="margin-top: 10px;">
                            ${Object.entries(info.byCategory).map(([category, count]) =>
                                `<div>${this.getCategoryLabel(category)}: <strong>${count}</strong></div>`
                            ).join('')}
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4>🕒 最近使用</h4>
                    <div style="max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px;">
                        ${info.recentlyUsed.map(snippet => `
                            <div style="padding: 8px; margin: 4px 0; background: white; border-radius: 4px; display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>${snippet.name}</strong><br>
                                    <small style="color: #666;">${snippet.channel}/${snippet.field}</small>
                                </div>
                                <small style="color: #888;">${new Date(snippet.updatedAt).toLocaleString('zh-CN')}</small>
                            </div>
                        `).join('') || '<div style="text-align: center; color: #666; padding: 20px;">暂无使用记录</div>'}
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn btn-primary" onclick="window.promptEditor.openEditor()">打开编辑器</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 点击遮罩关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * 清理所有缓存
     * @SERVICE 缓存清理服务
     */
    clearAllCaches() {
        this.searchCache.clear();
        this.renderCache.clear();
        console.log('🧹 所有缓存已清理');
    }

    /**
     * 获取缓存统计信息
     * @SERVICE 缓存统计服务
     */
    getCacheStats() {
        return {
            searchCache: {
                size: this.searchCache.size,
                keys: Array.from(this.searchCache.keys())
            },
            renderCache: {
                size: this.renderCache.size,
                keys: Array.from(this.renderCache.keys())
            },
            debounceTimers: {
                active: this.debounceTimers.size,
                keys: Array.from(this.debounceTimers.keys())
            }
        };
    }

    /**
     * 预加载常用搜索结果
     * @SERVICE 搜索预加载服务
     */
    preloadCommonSearches() {
        const commonTerms = ['customer', 'phone', 'email', 'address', 'klook', 'kkday'];

        commonTerms.forEach(term => {
            // 异步预加载，不阻塞主线程
            setTimeout(() => {
                this.filterSnippets(term);
            }, Math.random() * 1000); // 随机延迟避免同时执行
        });

        console.log('🚀 预加载常用搜索完成');
    }

    /**
     * 优化内存使用
     * @SERVICE 内存优化服务
     */
    optimizeMemoryUsage() {
        // 清理过期的防抖定时器
        this.debounceTimers.forEach((timer, key) => {
            if (timer && timer._destroyed) {
                this.debounceTimers.delete(key);
            }
        });

        // 限制缓存大小
        if (this.searchCache.size > 100) {
            const keysToDelete = Array.from(this.searchCache.keys()).slice(0, 50);
            keysToDelete.forEach(key => this.searchCache.delete(key));
            console.log('🧹 搜索缓存已优化');
        }

        if (this.renderCache.size > 50) {
            const keysToDelete = Array.from(this.renderCache.keys()).slice(0, 25);
            keysToDelete.forEach(key => this.renderCache.delete(key));
            console.log('🧹 渲染缓存已优化');
        }

        // 强制垃圾回收（如果可用）
        if (window.gc) {
            window.gc();
            console.log('🗑️ 垃圾回收已执行');
        }
    }

    /**
     * 性能监控
     * @SERVICE 性能监控服务
     */
    startPerformanceMonitoring() {
        // 监控搜索性能
        const originalFilterSnippets = this.filterSnippets.bind(this);
        this.filterSnippets = function(searchTerm) {
            const startTime = performance.now();
            const result = originalFilterSnippets(searchTerm);
            const endTime = performance.now();

            if (endTime - startTime > 100) { // 超过100ms记录
                console.warn(`⚠️ 搜索性能警告: "${searchTerm}" 耗时 ${(endTime - startTime).toFixed(2)}ms`);
            }

            return result;
        };

        // 监控渲染性能
        const originalRenderSnippetsList = this.renderSnippetsList.bind(this);
        this.renderSnippetsList = function() {
            const startTime = performance.now();
            const result = originalRenderSnippetsList();
            const endTime = performance.now();

            if (endTime - startTime > 50) { // 超过50ms记录
                console.warn(`⚠️ 渲染性能警告: 耗时 ${(endTime - startTime).toFixed(2)}ms`);
            }

            return result;
        };

        console.log('📊 性能监控已启动');
    }

    /**
     * 获取性能报告
     * @SERVICE 性能报告服务
     */
    getPerformanceReport() {
        const cacheStats = this.getCacheStats();
        const memoryInfo = performance.memory ? {
            usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
            totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
            jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
        } : null;

        return {
            timestamp: new Date().toISOString(),
            snippetsCount: this.promptSnippets.length,
            filteredCount: this.filteredSnippets.length,
            cacheStats: cacheStats,
            memoryInfo: memoryInfo,
            isEditorInitialized: this.isEditorInitialized
        };
    }

    /**
     * 刷新片段数据（重新从存储加载）
     * @SERVICE 数据同步服务
     */
    async refreshSnippetsData() {
        try {
            console.log('🔄 刷新片段数据...');

            // 重新从存储加载
            this.loadAllSnippetsFromStorage();

            // 更新过滤列表
            this.filteredSnippets = [...this.promptSnippets];

            // 重新渲染界面
            this.renderSnippetsList();

            // 如果当前有选中的片段，尝试重新选中
            if (this.currentSnippet && !this.currentSnippet.isNew) {
                const updatedSnippet = this.promptSnippets.find(s => s.id === this.currentSnippet.id);
                if (updatedSnippet) {
                    this.currentSnippet = updatedSnippet;
                    this.loadSnippetToForm(updatedSnippet);
                } else {
                    // 片段已被删除，重置表单
                    this.resetForm();
                }
            }

            console.log('✅ 片段数据刷新完成');
        } catch (error) {
            console.error('❌ 刷新片段数据失败:', error);
        }
    }

    /**
     * 获取所有片段的统计信息
     * @SERVICE 数据统计服务
     */
    getSnippetsStats() {
        const stats = {
            total: this.promptSnippets.length,
            byCategory: {},
            byChannel: {},
            defaultCount: 0,
            customCount: 0
        };

        this.promptSnippets.forEach(snippet => {
            // 按分类统计
            stats.byCategory[snippet.category] = (stats.byCategory[snippet.category] || 0) + 1;

            // 按渠道统计
            stats.byChannel[snippet.channel] = (stats.byChannel[snippet.channel] || 0) + 1;

            // 默认vs自定义统计
            if (snippet.id.startsWith('default_')) {
                stats.defaultCount++;
            } else {
                stats.customCount++;
            }
        });

        return stats;
    }

    /**
     * 导出当前所有片段数据
     * @SERVICE 数据导出服务
     */
    exportSnippetsData() {
        const exportData = {
            snippets: this.promptSnippets,
            stats: this.getSnippetsStats(),
            exportTime: new Date().toISOString(),
            version: '1.0'
        };

        return exportData;
    }

    /**
     * 批量更新片段数据
     * @SERVICE 批量数据更新服务
     */
    async batchUpdateSnippets(updates) {
        const results = [];

        for (const update of updates) {
            try {
                const snippet = this.promptSnippets.find(s => s.id === update.id);
                if (snippet) {
                    // 更新片段数据
                    Object.assign(snippet, update.data);
                    snippet.updatedAt = new Date().toISOString();

                    // 保存到存储
                    await this.saveSnippetToStorage(snippet);

                    results.push({ id: update.id, success: true });
                } else {
                    results.push({ id: update.id, success: false, error: '片段不存在' });
                }
            } catch (error) {
                results.push({ id: update.id, success: false, error: error.message });
            }
        }

        // 刷新界面
        this.renderSnippetsList();

        return results;
    }

    /**
     * 检查数据完整性
     * @SERVICE 数据完整性检查服务
     */
    validateDataIntegrity() {
        const issues = [];

        // 检查重复的渠道/字段组合
        const channelFieldMap = new Map();
        this.promptSnippets.forEach(snippet => {
            const key = `${snippet.channel}/${snippet.field}`;
            if (channelFieldMap.has(key)) {
                issues.push({
                    type: 'duplicate',
                    message: `重复的渠道/字段组合: ${key}`,
                    snippets: [channelFieldMap.get(key), snippet.id]
                });
            } else {
                channelFieldMap.set(key, snippet.id);
            }
        });

        // 检查必填字段
        this.promptSnippets.forEach(snippet => {
            if (!snippet.name || !snippet.template || !snippet.channel || !snippet.field) {
                issues.push({
                    type: 'missing_required',
                    message: `片段 ${snippet.id} 缺少必填字段`,
                    snippet: snippet.id
                });
            }
        });

        return {
            valid: issues.length === 0,
            issues: issues
        };
    }

    /**
     * 批量优化提示词
     * @SERVICE 批量优化服务
     * @param {string} orderContent - 订单内容
     * @returns {Object} 批量优化结果
     */
    async optimizePromptsForOrder(orderContent) {
        try {
            console.log('🚀 开始批量优化提示词...');

            // 1. 分析需要优化的字段
            const fieldsToOptimize = this.aiOptimizer.analyzeRequiredFields(orderContent);

            if (fieldsToOptimize.length === 0) {
                return {
                    success: true,
                    message: '未发现需要优化的字段',
                    results: []
                };
            }

            // 2. 显示进度界面
            this.showOptimizationProgress(fieldsToOptimize);

            // 3. 批量执行优化
            const optimizationResults = [];

            for (let i = 0; i < fieldsToOptimize.length; i++) {
                const fieldInfo = fieldsToOptimize[i];

                try {
                    // 更新进度
                    this.updateOptimizationProgress(i, fieldsToOptimize.length, `正在优化: ${fieldInfo.field}`);

                    // 执行优化
                    const result = await this.optimizeFieldPrompt(
                        fieldInfo.field,
                        orderContent,
                        fieldInfo.currentPrompt
                    );

                    optimizationResults.push({
                        ...result,
                        success: true,
                        fieldInfo: fieldInfo
                    });

                    // 短暂延迟，避免API限制
                    await this.delay(500);

                } catch (error) {
                    console.error(`优化字段 ${fieldInfo.field} 失败:`, error);
                    optimizationResults.push({
                        field: fieldInfo.field,
                        success: false,
                        error: error.message,
                        fieldInfo: fieldInfo
                    });
                }
            }

            // 4. 完成进度显示
            this.updateOptimizationProgress(fieldsToOptimize.length, fieldsToOptimize.length, '优化完成！');

            // 5. 显示结果
            setTimeout(() => {
                this.showOptimizationResults(optimizationResults);
            }, 1000);

            console.log('✅ 批量优化完成:', optimizationResults);

            return {
                success: true,
                results: optimizationResults,
                totalFields: fieldsToOptimize.length,
                successCount: optimizationResults.filter(r => r.success).length,
                failureCount: optimizationResults.filter(r => !r.success).length
            };

        } catch (error) {
            console.error('❌ 批量优化失败:', error);
            this.hideOptimizationProgress();

            return {
                success: false,
                error: error.message,
                results: []
            };
        }
    }

    /**
     * 显示优化进度界面
     * @SERVICE 进度显示服务
     */
    showOptimizationProgress(fieldsToOptimize) {
        const aiContent = document.getElementById('pe-ai-content');
        if (!aiContent) return;

        aiContent.innerHTML = `
            <div class="optimization-progress">
                <h4>🤖 AI 批量优化进行中...</h4>
                <div style="margin: 20px 0;">
                    <div class="progress-bar-container" style="background: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                        <div class="progress-bar" id="optimization-progress-bar" style="background: linear-gradient(90deg, #6f42c1, #8e44ad); height: 100%; width: 0%; transition: width 0.3s;"></div>
                    </div>
                    <div class="progress-text" id="optimization-progress-text" style="margin-top: 10px; font-size: 14px; color: #666;">
                        准备开始优化 ${fieldsToOptimize.length} 个字段...
                    </div>
                </div>
                <div class="fields-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px;">
                    ${fieldsToOptimize.map(field => `
                        <div class="field-item" id="field-${field.field}" style="padding: 8px; margin: 4px 0; background: #f8f9fa; border-radius: 4px; display: flex; justify-content: space-between; align-items: center;">
                            <span>${field.field}</span>
                            <span class="field-status" style="font-size: 12px; color: #666;">等待中...</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 更新优化进度
     * @SERVICE 进度更新服务
     */
    updateOptimizationProgress(current, total, message) {
        const progressBar = document.getElementById('optimization-progress-bar');
        const progressText = document.getElementById('optimization-progress-text');

        if (progressBar) {
            const percentage = (current / total) * 100;
            progressBar.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${message} (${current}/${total})`;
        }
    }

    /**
     * 延迟工具函数
     * @UTIL 延迟工具
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 显示优化结果
     * @SERVICE 结果显示服务
     */
    showOptimizationResults(results) {
        const aiContent = document.getElementById('pe-ai-content');
        if (!aiContent) return;

        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;

        aiContent.innerHTML = `
            <div class="optimization-results">
                <h4>🎉 优化完成</h4>
                <div class="results-summary" style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>总计字段:</span>
                        <span><strong>${totalCount}</strong></span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>成功优化:</span>
                        <span style="color: #28a745;"><strong>${successCount}</strong></span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>失败:</span>
                        <span style="color: #dc3545;"><strong>${totalCount - successCount}</strong></span>
                    </div>
                </div>

                <div class="results-list" style="max-height: 300px; overflow-y: auto;">
                    ${results.map(result => `
                        <div class="result-item" style="margin: 10px 0; padding: 12px; border: 1px solid #e9ecef; border-radius: 6px; ${result.success ? 'border-left: 4px solid #28a745;' : 'border-left: 4px solid #dc3545;'}">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>${result.field}</strong>
                                <span style="font-size: 12px; color: ${result.success ? '#28a745' : '#dc3545'};">
                                    ${result.success ? '✅ 成功' : '❌ 失败'}
                                </span>
                            </div>
                            ${result.success ? `
                                <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                    预期提升: +${result.expectedImprovement}%
                                </div>
                                <div style="font-size: 12px;">
                                    <strong>改进点:</strong> ${result.improvements ? result.improvements.slice(0, 2).join(', ') : '无'}
                                </div>
                                <div style="margin-top: 8px;">
                                    <button class="btn btn-sm btn-primary" onclick="window.promptEditor.showVersionComparison('${result.field}', \`${result.originalPrompt || ''}\`, \`${result.optimizedPrompt || ''}\`)">
                                        查看对比
                                    </button>
                                </div>
                            ` : `
                                <div style="font-size: 12px; color: #dc3545;">
                                    错误: ${result.error}
                                </div>
                            `}
                        </div>
                    `).join('')}
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-primary" onclick="window.promptEditor.aiOptimizer.hideOptimizationProgress()">
                        关闭结果
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 隐藏优化进度
     * @SERVICE 进度隐藏服务
     */
    hideOptimizationProgress() {
        const aiContent = document.getElementById('pe-ai-content');
        if (aiContent) {
            aiContent.innerHTML = `
                <div style="text-align: center; color: #666; padding: 40px 20px;">
                    <p>选择一个片段后，点击"分析优化"获取AI建议</p>
                </div>
            `;
        }
    }
}

class AIOptimizer {
    constructor(promptEditor, geminiService) {
        this.promptEditor = promptEditor;
        this.geminiService = geminiService;
        this.optimizationHistory = []; // 优化历史记录
    }

    /**
     * 分析订单内容，识别需要优化的字段
     * @SERVICE AI字段分析服务
     * @DEPENDENCY 依赖 fieldMapper、相关性计算算法
     * @INIT 初始化字段定义和描述信息
     *
     * 分析流程：
     * 1. 获取必填和可选字段定义
     * 2. 计算每个字段与订单内容的相关性
     * 3. 获取当前字段使用的提示词
     * 4. 计算优化优先级
     * 5. 按优先级排序返回结果
     *
     * @param {string} orderContent - 订单内容
     * @returns {Array} 需要优化的字段列表
     */
    analyzeRequiredFields(orderContent) {
        try {
            const fieldMapper = this.promptEditor.fieldMapper;
            const requiredFields = fieldMapper.getApiFieldDefinitions().required || [];
            const optionalFields = fieldMapper.getApiFieldDefinitions().optional || [];

            // 基于订单内容分析哪些字段需要优化
            const analysisResults = [];

            [...requiredFields, ...optionalFields].forEach(field => {
                const fieldInfo = fieldMapper.getApiFieldDefinitions().descriptions[field];
                if (fieldInfo) {
                    // 检查订单内容中是否包含该字段相关信息
                    const relevanceScore = this.calculateFieldRelevance(orderContent, field, fieldInfo);

                    if (relevanceScore > 0.3) { // 相关性阈值
                        analysisResults.push({
                            field: field,
                            relevance: relevanceScore,
                            description: fieldInfo.description,
                            currentPrompt: this.getCurrentPromptForField(field),
                            priority: this.calculateOptimizationPriority(field, relevanceScore)
                        });
                    }
                }
            });

            // 按优先级排序
            analysisResults.sort((a, b) => b.priority - a.priority);

            console.log('📊 字段分析结果:', analysisResults);
            return analysisResults;

        } catch (error) {
            console.error('❌ 字段分析失败:', error);
            return [];
        }
    }

    /**
     * 计算字段与订单内容的相关性
     * @UTIL 相关性计算工具
     */
    calculateFieldRelevance(orderContent, field, fieldInfo) {
        const content = orderContent.toLowerCase();
        let score = 0;

        // 基于字段名匹配
        if (content.includes(field.toLowerCase())) {
            score += 0.5;
        }

        // 基于字段描述中的关键词匹配
        if (fieldInfo.keywords) {
            fieldInfo.keywords.forEach(keyword => {
                if (content.includes(keyword.toLowerCase())) {
                    score += 0.2;
                }
            });
        }

        // 基于字段类型的启发式规则
        const fieldTypeRules = {
            'customer': ['客户', '姓名', '联系人', 'name'],
            'phone': ['电话', '手机', '联系', 'phone', 'tel'],
            'email': ['邮箱', '邮件', 'email', '@'],
            'address': ['地址', '位置', '地点', 'address'],
            'date': ['日期', '时间', '预约', 'date'],
            'channel': ['平台', '渠道', '来源', 'platform']
        };

        const keywords = fieldTypeRules[field] || [];
        keywords.forEach(keyword => {
            if (content.includes(keyword)) {
                score += 0.15;
            }
        });

        return Math.min(score, 1.0); // 限制最大值为1.0
    }

    /**
     * 获取字段当前使用的提示词
     * @UTIL 提示词获取工具
     */
    getCurrentPromptForField(field) {
        const snippets = this.promptEditor.promptSnippets;

        // 查找匹配的片段
        const matchingSnippet = snippets.find(snippet =>
            snippet.field === field ||
            snippet.usage === field ||
            snippet.name.toLowerCase().includes(field.toLowerCase())
        );

        return matchingSnippet ? matchingSnippet.template : null;
    }

    /**
     * 计算优化优先级
     * @UTIL 优先级计算工具
     */
    calculateOptimizationPriority(field, relevance) {
        // 基础优先级基于相关性
        let priority = relevance;

        // 核心字段加权
        const coreFields = ['customer', 'phone', 'email', 'address'];
        if (coreFields.includes(field)) {
            priority += 0.3;
        }

        // 检测类字段加权
        if (field === 'channel' || field.includes('detect')) {
            priority += 0.2;
        }

        return Math.min(priority, 1.0);
    }

    /**
     * 优化单个字段的提示词
     * @SERVICE AI提示词优化服务
     * @param {string} field - 字段名
     * @param {string} orderContent - 订单内容
     * @param {string} currentPrompt - 当前提示词
     * @returns {Object} 优化结果
     */
    async optimizeFieldPrompt(field, orderContent, currentPrompt = null) {
        try {
            console.log(`🤖 开始优化字段: ${field}`);

            // 获取当前提示词
            if (!currentPrompt) {
                currentPrompt = this.getCurrentPromptForField(field) || this.getDefaultPromptTemplate(field);
            }

            // 构建优化提示词
            const optimizationPrompt = this.buildOptimizationPrompt(field, orderContent, currentPrompt);

            // 调用Gemini API
            const response = await this.geminiService.generateContent(optimizationPrompt);

            if (response.success && response.content) {
                const optimizationResult = this.parseOptimizationResponse(response.content, field, currentPrompt);

                // 记录优化历史
                this.recordOptimization(field, currentPrompt, optimizationResult);

                console.log(`✅ 字段 ${field} 优化完成`);
                return optimizationResult;
            } else {
                throw new Error(response.error || 'AI优化失败');
            }

        } catch (error) {
            console.error(`❌ 优化字段 ${field} 失败:`, error);

            // 返回模拟优化结果作为降级方案
            return this.getMockOptimizationResult(field, currentPrompt, orderContent);
        }
    }

    /**
     * 构建优化提示词
     * @UTIL 优化提示词构建工具
     */
    buildOptimizationPrompt(field, orderContent, currentPrompt) {
        return `你是提示词优化专家。请基于以下订单内容优化字段提取提示词:

订单内容:
${orderContent}

目标字段: ${field}
当前提示词: ${currentPrompt}

请提供:
1. 优化后的提示词 (保持简洁清晰，适合AI理解)
2. 主要改进点说明 (3-5个要点)
3. 预期准确率提升百分比 (0-50%)

要求:
- 提高提取准确率，减少误判和遗漏
- 适配订单内容特征和语言风格
- 保持提示词简洁性，避免冗余
- 使用清晰的指令格式

请以JSON格式返回:
{
  "optimizedPrompt": "优化后的提示词",
  "improvements": ["改进点1", "改进点2", "改进点3"],
  "expectedImprovement": 数字,
  "reasoning": "优化理由说明"
}`;
    }

    /**
     * 解析AI优化响应
     * @UTIL 响应解析工具
     */
    parseOptimizationResponse(content, field, originalPrompt) {
        try {
            // 尝试解析JSON响应
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const result = JSON.parse(jsonMatch[0]);
                return {
                    field: field,
                    originalPrompt: originalPrompt,
                    optimizedPrompt: result.optimizedPrompt,
                    improvements: result.improvements || [],
                    expectedImprovement: result.expectedImprovement || 0,
                    reasoning: result.reasoning || '',
                    timestamp: new Date().toISOString()
                };
            }
        } catch (error) {
            console.warn('解析AI响应失败，使用文本解析:', error);
        }

        // 降级到文本解析
        return {
            field: field,
            originalPrompt: originalPrompt,
            optimizedPrompt: content.substring(0, 500), // 截取前500字符作为优化结果
            improvements: ['AI响应解析失败，请手动检查优化内容'],
            expectedImprovement: 10,
            reasoning: '基于AI文本响应的优化建议',
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 获取默认提示词模板
     * @UTIL 默认模板工具
     */
    getDefaultPromptTemplate(field) {
        const templates = {
            'customer': '请从文本中提取客户姓名信息',
            'phone': '请从文本中提取电话号码信息',
            'email': '请从文本中提取邮箱地址信息',
            'address': '请从文本中提取地址信息',
            'date': '请从文本中提取日期时间信息',
            'channel': '请识别文本来源平台或渠道'
        };

        return templates[field] || `请从文本中提取${field}相关信息`;
    }

    /**
     * 记录优化历史
     * @SERVICE 优化历史记录服务
     */
    recordOptimization(field, originalPrompt, optimizationResult) {
        this.optimizationHistory.push({
            field: field,
            originalPrompt: originalPrompt,
            optimizationResult: optimizationResult,
            timestamp: new Date().toISOString()
        });

        // 保持最近50条记录
        if (this.optimizationHistory.length > 50) {
            this.optimizationHistory = this.optimizationHistory.slice(-50);
        }
    }

    /**
     * 获取模拟优化结果（降级方案）
     * @UTIL 模拟数据工具
     */
    getMockOptimizationResult(field, currentPrompt, orderContent) {
        const mockImprovements = [
            '增加了上下文理解能力',
            '优化了字段识别准确性',
            '改进了多语言支持',
            '增强了边界情况处理'
        ];

        const mockOptimizedPrompt = currentPrompt ?
            `${currentPrompt}\n\n请特别注意从以下类型的内容中准确提取信息，确保结果的完整性和准确性。` :
            this.getDefaultPromptTemplate(field) + '\n\n请确保提取结果准确完整。';

        return {
            field: field,
            originalPrompt: currentPrompt,
            optimizedPrompt: mockOptimizedPrompt,
            improvements: mockImprovements.slice(0, 3),
            expectedImprovement: Math.floor(Math.random() * 20) + 10, // 10-30%
            reasoning: '基于字段特征和内容分析的优化建议（模拟结果）',
            timestamp: new Date().toISOString(),
            isMock: true
        };
    }

    /**
     * 批量优化提示词
     * @SERVICE 批量优化服务
     * @param {string} orderContent - 订单内容
     * @returns {Object} 批量优化结果
     */
    async optimizePromptsForOrder(orderContent) {
        try {
            console.log('🚀 开始批量优化提示词...');

            // 1. 分析需要优化的字段
            const fieldsToOptimize = this.analyzeRequiredFields(orderContent);

            if (fieldsToOptimize.length === 0) {
                return {
                    success: true,
                    message: '未发现需要优化的字段',
                    results: []
                };
            }

            // 2. 显示进度界面
            this.showOptimizationProgress(fieldsToOptimize);

            // 3. 批量执行优化
            const optimizationResults = [];

            for (let i = 0; i < fieldsToOptimize.length; i++) {
                const fieldInfo = fieldsToOptimize[i];

                try {
                    // 更新进度
                    this.updateOptimizationProgress(i, fieldsToOptimize.length, `正在优化: ${fieldInfo.field}`);

                    // 执行优化
                    const result = await this.optimizeFieldPrompt(
                        fieldInfo.field,
                        orderContent,
                        fieldInfo.currentPrompt
                    );

                    optimizationResults.push({
                        ...result,
                        success: true,
                        fieldInfo: fieldInfo
                    });

                    // 短暂延迟，避免API限制
                    await this.delay(500);

                } catch (error) {
                    console.error(`优化字段 ${fieldInfo.field} 失败:`, error);
                    optimizationResults.push({
                        field: fieldInfo.field,
                        success: false,
                        error: error.message,
                        fieldInfo: fieldInfo
                    });
                }
            }

            // 4. 完成进度显示
            this.updateOptimizationProgress(fieldsToOptimize.length, fieldsToOptimize.length, '优化完成！');

            // 5. 显示结果
            setTimeout(() => {
                this.showOptimizationResults(optimizationResults);
            }, 1000);

            console.log('✅ 批量优化完成:', optimizationResults);

            return {
                success: true,
                results: optimizationResults,
                totalFields: fieldsToOptimize.length,
                successCount: optimizationResults.filter(r => r.success).length,
                failureCount: optimizationResults.filter(r => !r.success).length
            };

        } catch (error) {
            console.error('❌ 批量优化失败:', error);
            this.hideOptimizationProgress();

            return {
                success: false,
                error: error.message,
                results: []
            };
        }
    }

    /**
     * 显示优化进度界面
     * @SERVICE 进度显示服务
     */
    showOptimizationProgress(fieldsToOptimize) {
        const aiContent = document.getElementById('pe-ai-content');
        if (!aiContent) return;

        aiContent.innerHTML = `
            <div class="optimization-progress">
                <h4>🤖 AI 批量优化进行中...</h4>
                <div style="margin: 20px 0;">
                    <div class="progress-bar-container" style="background: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                        <div class="progress-bar" id="optimization-progress-bar" style="background: linear-gradient(90deg, #6f42c1, #8e44ad); height: 100%; width: 0%; transition: width 0.3s;"></div>
                    </div>
                    <div class="progress-text" id="optimization-progress-text" style="margin-top: 10px; font-size: 14px; color: #666;">
                        准备开始优化 ${fieldsToOptimize.length} 个字段...
                    </div>
                </div>
                <div class="fields-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px;">
                    ${fieldsToOptimize.map(field => `
                        <div class="field-item" id="field-${field.field}" style="padding: 8px; margin: 4px 0; background: #f8f9fa; border-radius: 4px; display: flex; justify-content: space-between; align-items: center;">
                            <span>${field.field}</span>
                            <span class="field-status" style="font-size: 12px; color: #666;">等待中...</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 更新优化进度
     * @SERVICE 进度更新服务
     */
    updateOptimizationProgress(current, total, message) {
        const progressBar = document.getElementById('optimization-progress-bar');
        const progressText = document.getElementById('optimization-progress-text');

        if (progressBar) {
            const percentage = (current / total) * 100;
            progressBar.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${message} (${current}/${total})`;
        }

        // 更新字段状态
        if (current > 0) {
            const fieldsToOptimize = this.promptEditor.aiOptimizer.optimizationHistory.slice(-total);
            if (fieldsToOptimize[current - 1]) {
                const fieldElement = document.getElementById(`field-${fieldsToOptimize[current - 1].field}`);
                if (fieldElement) {
                    const statusElement = fieldElement.querySelector('.field-status');
                    if (statusElement) {
                        statusElement.textContent = '✅ 完成';
                        statusElement.style.color = '#28a745';
                    }
                }
            }
        }
    }

    /**
     * 隐藏优化进度
     * @SERVICE 进度隐藏服务
     */
    hideOptimizationProgress() {
        const aiContent = document.getElementById('pe-ai-content');
        if (aiContent) {
            aiContent.innerHTML = `
                <div style="text-align: center; color: #666; padding: 40px 20px;">
                    <p>选择一个片段后，点击"分析优化"获取AI建议</p>
                </div>
            `;
        }
    }

    /**
     * 显示优化结果
     * @SERVICE 结果显示服务
     */
    showOptimizationResults(results) {
        const aiContent = document.getElementById('pe-ai-content');
        if (!aiContent) return;

        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;

        aiContent.innerHTML = `
            <div class="optimization-results">
                <h4>🎉 优化完成</h4>
                <div class="results-summary" style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>总计字段:</span>
                        <span><strong>${totalCount}</strong></span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>成功优化:</span>
                        <span style="color: #28a745;"><strong>${successCount}</strong></span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>失败:</span>
                        <span style="color: #dc3545;"><strong>${totalCount - successCount}</strong></span>
                    </div>
                </div>

                <div class="results-list" style="max-height: 300px; overflow-y: auto;">
                    ${results.map(result => `
                        <div class="result-item" style="margin: 10px 0; padding: 12px; border: 1px solid #e9ecef; border-radius: 6px; ${result.success ? 'border-left: 4px solid #28a745;' : 'border-left: 4px solid #dc3545;'}">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>${result.field}</strong>
                                <span style="font-size: 12px; color: ${result.success ? '#28a745' : '#dc3545'};">
                                    ${result.success ? '✅ 成功' : '❌ 失败'}
                                </span>
                            </div>
                            ${result.success ? `
                                <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                    预期提升: +${result.expectedImprovement}%
                                </div>
                                <div style="font-size: 12px;">
                                    <strong>改进点:</strong> ${result.improvements.slice(0, 2).join(', ')}
                                </div>
                                <div style="margin-top: 8px;">
                                    <button class="btn btn-sm btn-primary" onclick="promptEditor.showVersionComparison('${result.field}', \`${result.originalPrompt}\`, \`${result.optimizedPrompt}\`)">
                                        查看对比
                                    </button>
                                </div>
                            ` : `
                                <div style="font-size: 12px; color: #dc3545;">
                                    错误: ${result.error}
                                </div>
                            `}
                        </div>
                    `).join('')}
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-primary" onclick="promptEditor.aiOptimizer.hideOptimizationProgress()">
                        关闭结果
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 延迟工具函数
     * @UTIL 延迟工具
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 模块工厂函数
function createPromptEditorModule(container) {
    const configManager = container.get('config');
    const fieldMapper = container.get('fieldMapper');
    const promptComposer = container.get('promptComposer');
    const localStorageManager = container.get('localStorageManager');
    const geminiService = container.get('gemini');
    return new PromptEditor(configManager, fieldMapper, promptComposer, localStorageManager, geminiService);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('promptEditor', createPromptEditorModule, ['config', 'fieldMapper', 'promptComposer', 'localStorageManager', 'gemini']);
    console.log('📦 PromptEditor 已注册到模块容器');
}
