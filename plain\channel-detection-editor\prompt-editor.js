/**
 * 提示词片段编辑器模块 - Refactored
 */
class PromptEditor {
    constructor(configManager, fieldMapper, promptComposer, localStorageManager, geminiService) {
        if (!configManager || !fieldMapper || !promptComposer || !localStorageManager || !geminiService) {
            throw new Error("PromptEditor requires all dependencies to be injected.");
        }
        this.configManager = configManager;
        this.fieldMapper = fieldMapper;
        this.promptComposer = promptComposer;
        this.localStorageManager = localStorageManager;
        this.geminiService = geminiService;

        this.promptSnippets = this.loadDefaultSnippets();
        this.requiredFields = this.fieldMapper.getApiFieldDefinitions().required || [];
        this.optionalFields = this.fieldMapper.getApiFieldDefinitions().optional || [];
        this.fieldDefinitions = this.fieldMapper.getApiFieldDefinitions().descriptions || {};

        this.aiOptimizer = new AIOptimizer(this, this.geminiService);

        this.initializeEditor();
        setTimeout(() => this.loadAllSnippetsFromStorage(), 1000);
    }

    initializeEditor() {
        console.log('PromptEditor initialized (Refactored)');
    }

    /**
     * 加载默认的提示词片段
     * @returns {Array} 默认提示词片段数组
     */
    loadDefaultSnippets() {
        return [
            {
                id: 'default_booking_extract',
                name: '基础订单信息提取',
                template: '请从以下订单文本中提取关键信息：\n{input}\n\n请提取并返回以下字段：\n- 客户姓名\n- 联系电话\n- 服务日期\n- 服务类型',
                category: 'extraction',
                usage: 'booking_info'
            },
            {
                id: 'channel_detection',
                name: '渠道检测',
                template: '请识别以下订单来源于哪个平台：\n{input}\n\n可能的平台包括：Klook, KKday, 携程, 途牛等。',
                category: 'detection',
                usage: 'channel_detection'
            }
        ];
    }

    /**
     * 数据适配层：将LocalStorageManager的对象树格式转换为编辑器数组格式
     * @UTIL 数据格式适配工具
     * @param {Object} promptsObject - LocalStorageManager返回的对象树 {channel: {field: {content, ...}}}
     * @returns {Array} 编辑器使用的片段数组格式
     */
    mapPromptsObjectToSnippetsArray(promptsObject) {
        const snippets = [];

        if (!promptsObject || typeof promptsObject !== 'object') {
            return snippets;
        }

        // 遍历渠道和字段，转换为片段数组
        Object.entries(promptsObject).forEach(([channel, fields]) => {
            if (fields && typeof fields === 'object') {
                Object.entries(fields).forEach(([field, promptData]) => {
                    if (promptData && promptData.content) {
                        snippets.push({
                            id: `${channel}_${field}_${Date.now()}`, // 生成唯一ID
                            name: promptData.name || `${channel} - ${field}`, // 显示名称
                            template: promptData.content, // 提示词内容
                            category: this.inferCategory(field), // 推断分类
                            usage: promptData.usage || field, // 使用场景
                            channel: channel, // 所属渠道
                            field: field, // 对应字段
                            createdAt: promptData.createdAt || new Date().toISOString(),
                            updatedAt: promptData.updatedAt || new Date().toISOString()
                        });
                    }
                });
            }
        });

        return snippets;
    }

    /**
     * 推断片段分类
     * @UTIL 分类推断工具
     * @param {string} field - 字段名
     * @returns {string} 分类名称
     */
    inferCategory(field) {
        const extractionFields = ['customer', 'phone', 'email', 'address', 'date', 'time'];
        const detectionFields = ['channel', 'platform', 'source'];

        if (extractionFields.some(f => field.toLowerCase().includes(f))) {
            return 'extraction';
        }
        if (detectionFields.some(f => field.toLowerCase().includes(f))) {
            return 'detection';
        }
        return 'custom';
    }

    /**
     * 数据适配层：将编辑器片段格式转换为存储格式
     * @UTIL 数据格式适配工具
     * @param {Object} snippet - 编辑器片段对象
     * @returns {Object} 存储格式的数据
     */
    mapSnippetToStorage(snippet) {
        return {
            content: snippet.template,
            field: snippet.field,
            channel: snippet.channel,
            name: snippet.name,
            usage: snippet.usage,
            category: snippet.category,
            createdAt: snippet.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
    }

    /**
     * 从本地存储加载所有提示词片段
     * @SERVICE 提示词片段加载服务
     */
    loadAllSnippetsFromStorage() {
        try {
            const storedPromptsObject = this.localStorageManager.getAllPrompts();
            console.log('📥 从存储加载的原始数据:', storedPromptsObject);

            // 使用适配层转换数据格式
            const storedSnippets = this.mapPromptsObjectToSnippetsArray(storedPromptsObject);

            // 合并默认片段和存储的片段
            this.promptSnippets = [...this.loadDefaultSnippets(), ...storedSnippets];

            console.log('✅ 提示词片段加载完成:', this.promptSnippets.length, '个片段');
            console.log('📋 片段详情:', this.promptSnippets.map(s => `${s.name} (${s.channel}/${s.field})`));
        } catch (error) {
            console.warn('⚠️ 加载提示词片段失败:', error);
            this.promptSnippets = this.loadDefaultSnippets();
        }
    }

    /**
     * 保存片段到本地存储
     * @SERVICE 片段保存服务
     * @param {Object} snippet - 要保存的片段
     */
    saveSnippetToStorage(snippet) {
        try {
            const storageData = this.mapSnippetToStorage(snippet);
            const result = this.localStorageManager.savePrompt(
                snippet.channel,
                snippet.field,
                storageData.content
            );

            console.log('💾 片段保存成功:', snippet.name, result);

            // 触发缓存清理
            this.promptComposer.clearCache();

            return result;
        } catch (error) {
            console.error('❌ 保存片段失败:', error);
            throw error;
        }
    }

    /**
     * 从存储中删除片段
     * @SERVICE 片段删除服务
     * @param {Object} snippet - 要删除的片段
     */
    deleteSnippetFromStorage(snippet) {
        try {
            const result = this.localStorageManager.deletePrompt(snippet.channel, snippet.field);
            console.log('🗑️ 片段删除成功:', snippet.name);

            // 触发缓存清理
            this.promptComposer.clearCache();

            return result;
        } catch (error) {
            console.error('❌ 删除片段失败:', error);
            throw error;
        }
    }

    /**
     * 打开提示词编辑器主界面
     * @SERVICE 编辑器界面服务
     */
    openEditor() {
        console.log('📝 打开提示词编辑器');
        this.closeEditor(); // 确保关闭已存在的编辑器
        this.createEditorModal();
    }

    /**
     * 关闭编辑器
     * @LIFECYCLE 编辑器生命周期管理
     */
    closeEditor() {
        const existingModal = document.getElementById('prompt-editor-modal');
        if (existingModal) {
            existingModal.remove();
        }
    }

    /**
     * 创建编辑器模态框
     * @LIFECYCLE 编辑器界面创建
     */
    createEditorModal() {
        const modal = document.createElement('div');
        modal.id = 'prompt-editor-modal';
        modal.className = 'prompt-editor-modal';

        modal.innerHTML = this.getEditorHTML();
        document.body.appendChild(modal);

        // 绑定事件
        this.bindEditorEvents();

        // 点击遮罩关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeEditor();
            }
        });

        // 初始化界面数据
        this.initializeEditorData();
    }

    /**
     * 获取编辑器HTML结构
     * @UTIL 界面模板生成
     */
    getEditorHTML() {
        return `
            <div class="prompt-editor-content">
                <div class="prompt-editor-header">
                    <h2>📝 提示词片段编辑器</h2>
                    <button class="prompt-editor-close" id="pe-close-btn">×</button>
                </div>

                <div class="prompt-editor-body">
                    <!-- 左侧片段列表 -->
                    <div class="prompt-snippets-panel">
                        <div class="snippets-header">
                            <input type="text" class="snippets-search" id="pe-search" placeholder="搜索片段...">
                            <div class="snippets-filters" id="pe-filters">
                                <button class="filter-tag active" data-filter="all">全部</button>
                                <button class="filter-tag" data-filter="extraction">提取</button>
                                <button class="filter-tag" data-filter="detection">检测</button>
                                <button class="filter-tag" data-filter="custom">自定义</button>
                            </div>
                        </div>
                        <div class="snippets-list" id="pe-snippets-list">
                            <!-- 片段列表将在这里动态生成 -->
                        </div>
                        <div style="padding: 10px;">
                            <button class="btn btn-primary btn-sm" id="pe-add-snippet" style="width: 100%;">+ 新增片段</button>
                        </div>
                    </div>

                    <!-- 中间编辑区域 -->
                    <div class="prompt-editor-panel">
                        <div class="editor-header">
                            <h3 id="pe-editor-title">选择一个片段进行编辑</h3>
                        </div>
                        <div class="editor-form" id="pe-editor-form">
                            <div class="form-group">
                                <label class="form-label">片段名称</label>
                                <input type="text" class="form-input" id="pe-snippet-name" placeholder="输入片段名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">所属渠道</label>
                                <input type="text" class="form-input" id="pe-snippet-channel" placeholder="如: klook, kkday, generic">
                            </div>
                            <div class="form-group">
                                <label class="form-label">对应字段</label>
                                <input type="text" class="form-input" id="pe-snippet-field" placeholder="如: customer, phone, address">
                            </div>
                            <div class="form-group">
                                <label class="form-label">分类</label>
                                <select class="form-select" id="pe-snippet-category">
                                    <option value="extraction">信息提取</option>
                                    <option value="detection">渠道检测</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">使用场景</label>
                                <input type="text" class="form-input" id="pe-snippet-usage" placeholder="描述使用场景">
                            </div>
                            <div class="form-group">
                                <label class="form-label">提示词模板</label>
                                <textarea class="form-textarea" id="pe-snippet-template" placeholder="输入提示词模板内容..."></textarea>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button class="btn btn-primary" id="pe-save-btn">💾 保存</button>
                            <button class="btn btn-danger" id="pe-delete-btn">🗑️ 删除</button>
                            <button class="btn btn-secondary" id="pe-reset-btn">🔄 重置</button>
                        </div>
                    </div>

                    <!-- 右侧AI建议区 -->
                    <div class="ai-suggestions-panel">
                        <div class="ai-header">
                            <h3>🤖 AI 优化建议</h3>
                            <button class="btn btn-sm btn-primary" id="pe-ai-analyze">分析优化</button>
                        </div>
                        <div class="ai-content" id="pe-ai-content">
                            <div style="text-align: center; color: #666; padding: 40px 20px;">
                                <p>选择一个片段后，点击"分析优化"获取AI建议</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定编辑器事件
     * @EVENT_HANDLER 编辑器事件绑定
     */
    bindEditorEvents() {
        // 关闭按钮
        document.getElementById('pe-close-btn').addEventListener('click', () => this.closeEditor());

        // 搜索功能
        document.getElementById('pe-search').addEventListener('input', (e) => {
            this.filterSnippets(e.target.value);
        });

        // 分类过滤
        document.querySelectorAll('#pe-filters .filter-tag').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 更新激活状态
                document.querySelectorAll('#pe-filters .filter-tag').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                // 执行过滤
                this.filterSnippetsByCategory(e.target.dataset.filter);
            });
        });

        // 新增片段
        document.getElementById('pe-add-snippet').addEventListener('click', () => this.createNewSnippet());

        // 表单操作
        document.getElementById('pe-save-btn').addEventListener('click', () => this.saveCurrentSnippet());
        document.getElementById('pe-delete-btn').addEventListener('click', () => this.deleteCurrentSnippet());
        document.getElementById('pe-reset-btn').addEventListener('click', () => this.resetForm());

        // AI分析
        document.getElementById('pe-ai-analyze').addEventListener('click', () => this.analyzeCurrentSnippet());
    }

    /**
     * 初始化编辑器数据
     * @LIFECYCLE 编辑器数据初始化
     */
    initializeEditorData() {
        this.currentSnippet = null; // 当前编辑的片段
        this.filteredSnippets = [...this.promptSnippets]; // 过滤后的片段列表
        this.renderSnippetsList();
    }

    /**
     * 渲染片段列表
     * @SERVICE 片段列表渲染服务
     */
    renderSnippetsList() {
        const listContainer = document.getElementById('pe-snippets-list');

        if (this.filteredSnippets.length === 0) {
            listContainer.innerHTML = `
                <div style="text-align: center; color: #666; padding: 20px;">
                    <p>暂无片段</p>
                    <p style="font-size: 12px;">点击下方"新增片段"开始创建</p>
                </div>
            `;
            return;
        }

        listContainer.innerHTML = this.filteredSnippets.map(snippet => `
            <div class="snippet-item" data-snippet-id="${snippet.id}">
                <div class="snippet-name">${snippet.name}</div>
                <div class="snippet-meta">
                    <span>${snippet.channel}/${snippet.field}</span>
                    <span class="category-${snippet.category}">${this.getCategoryLabel(snippet.category)}</span>
                </div>
            </div>
        `).join('');

        // 绑定片段点击事件
        listContainer.querySelectorAll('.snippet-item').forEach(item => {
            item.addEventListener('click', () => {
                const snippetId = item.dataset.snippetId;
                this.selectSnippet(snippetId);
            });
        });
    }

    /**
     * 获取分类标签
     * @UTIL 分类标签工具
     */
    getCategoryLabel(category) {
        const labels = {
            'extraction': '提取',
            'detection': '检测',
            'custom': '自定义'
        };
        return labels[category] || category;
    }

    /**
     * 过滤片段（按搜索关键词）
     * @SERVICE 片段搜索服务
     */
    filterSnippets(searchTerm) {
        const term = searchTerm.toLowerCase().trim();

        if (!term) {
            this.filteredSnippets = [...this.promptSnippets];
        } else {
            this.filteredSnippets = this.promptSnippets.filter(snippet =>
                snippet.name.toLowerCase().includes(term) ||
                snippet.channel.toLowerCase().includes(term) ||
                snippet.field.toLowerCase().includes(term) ||
                snippet.template.toLowerCase().includes(term)
            );
        }

        this.renderSnippetsList();
    }

    /**
     * 按分类过滤片段
     * @SERVICE 片段分类过滤服务
     */
    filterSnippetsByCategory(category) {
        if (category === 'all') {
            this.filteredSnippets = [...this.promptSnippets];
        } else {
            this.filteredSnippets = this.promptSnippets.filter(snippet =>
                snippet.category === category
            );
        }

        this.renderSnippetsList();
    }

    /**
     * 选择片段进行编辑
     * @SERVICE 片段选择服务
     */
    selectSnippet(snippetId) {
        // 更新列表中的激活状态
        document.querySelectorAll('.snippet-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-snippet-id="${snippetId}"]`).classList.add('active');

        // 查找片段数据
        this.currentSnippet = this.promptSnippets.find(s => s.id === snippetId);

        if (this.currentSnippet) {
            this.loadSnippetToForm(this.currentSnippet);
        }
    }

    /**
     * 将片段数据加载到表单
     * @SERVICE 表单数据加载服务
     */
    loadSnippetToForm(snippet) {
        document.getElementById('pe-editor-title').textContent = `编辑片段: ${snippet.name}`;
        document.getElementById('pe-snippet-name').value = snippet.name;
        document.getElementById('pe-snippet-channel').value = snippet.channel;
        document.getElementById('pe-snippet-field').value = snippet.field;
        document.getElementById('pe-snippet-category').value = snippet.category;
        document.getElementById('pe-snippet-usage').value = snippet.usage || '';
        document.getElementById('pe-snippet-template').value = snippet.template;

        // 启用删除按钮
        document.getElementById('pe-delete-btn').disabled = false;
    }

    /**
     * 创建新片段
     * @SERVICE 新片段创建服务
     */
    createNewSnippet() {
        // 清空表单
        this.resetForm();

        // 设置默认值
        document.getElementById('pe-editor-title').textContent = '新增片段';
        document.getElementById('pe-snippet-channel').value = 'generic';
        document.getElementById('pe-snippet-category').value = 'custom';

        // 清除列表选中状态
        document.querySelectorAll('.snippet-item').forEach(item => {
            item.classList.remove('active');
        });

        // 创建临时片段对象
        this.currentSnippet = {
            id: `new_${Date.now()}`,
            name: '',
            channel: 'generic',
            field: '',
            category: 'custom',
            usage: '',
            template: '',
            isNew: true
        };

        // 禁用删除按钮
        document.getElementById('pe-delete-btn').disabled = true;
    }

    /**
     * 重置表单
     * @SERVICE 表单重置服务
     */
    resetForm() {
        document.getElementById('pe-editor-title').textContent = '选择一个片段进行编辑';
        document.getElementById('pe-snippet-name').value = '';
        document.getElementById('pe-snippet-channel').value = '';
        document.getElementById('pe-snippet-field').value = '';
        document.getElementById('pe-snippet-category').value = 'extraction';
        document.getElementById('pe-snippet-usage').value = '';
        document.getElementById('pe-snippet-template').value = '';

        this.currentSnippet = null;
        document.getElementById('pe-delete-btn').disabled = true;
    }

    /**
     * 保存当前片段
     * @SERVICE 片段保存服务
     */
    async saveCurrentSnippet() {
        try {
            // 获取表单数据
            const formData = this.getFormData();

            // 验证表单
            const validation = this.validateForm(formData);
            if (!validation.valid) {
                this.showError(validation.message);
                return;
            }

            // 更新当前片段数据
            if (this.currentSnippet) {
                Object.assign(this.currentSnippet, formData);
                this.currentSnippet.updatedAt = new Date().toISOString();

                if (this.currentSnippet.isNew) {
                    // 新增片段
                    this.currentSnippet.id = `${formData.channel}_${formData.field}_${Date.now()}`;
                    this.currentSnippet.createdAt = new Date().toISOString();
                    delete this.currentSnippet.isNew;

                    // 添加到内存列表
                    this.promptSnippets.push(this.currentSnippet);
                    this.filteredSnippets.push(this.currentSnippet);
                }

                // 保存到存储
                await this.saveSnippetToStorage(this.currentSnippet);

                // 更新界面
                this.renderSnippetsList();
                this.selectSnippet(this.currentSnippet.id);

                this.showSuccess('片段保存成功！');
            }
        } catch (error) {
            console.error('保存片段失败:', error);
            this.showError('保存失败: ' + error.message);
        }
    }

    /**
     * 删除当前片段
     * @SERVICE 片段删除服务
     */
    async deleteCurrentSnippet() {
        if (!this.currentSnippet || this.currentSnippet.isNew) {
            return;
        }

        if (!confirm(`确定要删除片段"${this.currentSnippet.name}"吗？`)) {
            return;
        }

        try {
            // 从存储删除
            await this.deleteSnippetFromStorage(this.currentSnippet);

            // 从内存列表删除
            this.promptSnippets = this.promptSnippets.filter(s => s.id !== this.currentSnippet.id);
            this.filteredSnippets = this.filteredSnippets.filter(s => s.id !== this.currentSnippet.id);

            // 重置表单
            this.resetForm();

            // 更新界面
            this.renderSnippetsList();

            this.showSuccess('片段删除成功！');
        } catch (error) {
            console.error('删除片段失败:', error);
            this.showError('删除失败: ' + error.message);
        }
    }

    /**
     * 获取表单数据
     * @UTIL 表单数据获取工具
     */
    getFormData() {
        return {
            name: document.getElementById('pe-snippet-name').value.trim(),
            channel: document.getElementById('pe-snippet-channel').value.trim(),
            field: document.getElementById('pe-snippet-field').value.trim(),
            category: document.getElementById('pe-snippet-category').value,
            usage: document.getElementById('pe-snippet-usage').value.trim(),
            template: document.getElementById('pe-snippet-template').value.trim()
        };
    }

    /**
     * 验证表单数据
     * @UTIL 表单验证工具
     */
    validateForm(data) {
        if (!data.name) {
            return { valid: false, message: '请输入片段名称' };
        }

        if (!data.channel) {
            return { valid: false, message: '请输入所属渠道' };
        }

        if (!data.field) {
            return { valid: false, message: '请输入对应字段' };
        }

        if (!data.template) {
            return { valid: false, message: '请输入提示词模板' };
        }

        // 检查是否与现有片段冲突（除了当前编辑的片段）
        const existingSnippet = this.promptSnippets.find(s =>
            s.channel === data.channel &&
            s.field === data.field &&
            s.id !== (this.currentSnippet?.id)
        );

        if (existingSnippet) {
            return {
                valid: false,
                message: `渠道"${data.channel}"的字段"${data.field}"已存在片段，请使用不同的渠道/字段组合`
            };
        }

        return { valid: true };
    }

    /**
     * 显示成功消息
     * @UTIL 消息提示工具
     */
    showSuccess(message) {
        // 简单的成功提示实现
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: #28a745; color: white; padding: 12px 20px;
            border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.remove(), 3000);
    }

    /**
     * 显示错误消息
     * @UTIL 消息提示工具
     */
    showError(message) {
        // 简单的错误提示实现
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: #dc3545; color: white; padding: 12px 20px;
            border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.remove(), 5000);
    }

    /**
     * 分析当前片段（AI功能占位）
     * @SERVICE AI分析服务
     */
    analyzeCurrentSnippet() {
        if (!this.currentSnippet) {
            this.showError('请先选择一个片段');
            return;
        }

        // 暂时显示占位内容
        document.getElementById('pe-ai-content').innerHTML = `
            <div style="padding: 20px;">
                <h4>🤖 分析结果</h4>
                <p><strong>片段:</strong> ${this.currentSnippet.name}</p>
                <p><strong>分析状态:</strong> AI功能开发中...</p>
                <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <small>此功能将在Phase 2中实现，包括：</small>
                    <ul style="margin: 5px 0; padding-left: 20px; font-size: 12px;">
                        <li>智能优化建议</li>
                        <li>版本对比</li>
                        <li>效果预测</li>
                    </ul>
                </div>
            </div>
        `;
    }

    /**
     * 刷新片段数据（重新从存储加载）
     * @SERVICE 数据同步服务
     */
    async refreshSnippetsData() {
        try {
            console.log('🔄 刷新片段数据...');

            // 重新从存储加载
            this.loadAllSnippetsFromStorage();

            // 更新过滤列表
            this.filteredSnippets = [...this.promptSnippets];

            // 重新渲染界面
            this.renderSnippetsList();

            // 如果当前有选中的片段，尝试重新选中
            if (this.currentSnippet && !this.currentSnippet.isNew) {
                const updatedSnippet = this.promptSnippets.find(s => s.id === this.currentSnippet.id);
                if (updatedSnippet) {
                    this.currentSnippet = updatedSnippet;
                    this.loadSnippetToForm(updatedSnippet);
                } else {
                    // 片段已被删除，重置表单
                    this.resetForm();
                }
            }

            console.log('✅ 片段数据刷新完成');
        } catch (error) {
            console.error('❌ 刷新片段数据失败:', error);
        }
    }

    /**
     * 获取所有片段的统计信息
     * @SERVICE 数据统计服务
     */
    getSnippetsStats() {
        const stats = {
            total: this.promptSnippets.length,
            byCategory: {},
            byChannel: {},
            defaultCount: 0,
            customCount: 0
        };

        this.promptSnippets.forEach(snippet => {
            // 按分类统计
            stats.byCategory[snippet.category] = (stats.byCategory[snippet.category] || 0) + 1;

            // 按渠道统计
            stats.byChannel[snippet.channel] = (stats.byChannel[snippet.channel] || 0) + 1;

            // 默认vs自定义统计
            if (snippet.id.startsWith('default_')) {
                stats.defaultCount++;
            } else {
                stats.customCount++;
            }
        });

        return stats;
    }

    /**
     * 导出当前所有片段数据
     * @SERVICE 数据导出服务
     */
    exportSnippetsData() {
        const exportData = {
            snippets: this.promptSnippets,
            stats: this.getSnippetsStats(),
            exportTime: new Date().toISOString(),
            version: '1.0'
        };

        return exportData;
    }

    /**
     * 批量更新片段数据
     * @SERVICE 批量数据更新服务
     */
    async batchUpdateSnippets(updates) {
        const results = [];

        for (const update of updates) {
            try {
                const snippet = this.promptSnippets.find(s => s.id === update.id);
                if (snippet) {
                    // 更新片段数据
                    Object.assign(snippet, update.data);
                    snippet.updatedAt = new Date().toISOString();

                    // 保存到存储
                    await this.saveSnippetToStorage(snippet);

                    results.push({ id: update.id, success: true });
                } else {
                    results.push({ id: update.id, success: false, error: '片段不存在' });
                }
            } catch (error) {
                results.push({ id: update.id, success: false, error: error.message });
            }
        }

        // 刷新界面
        this.renderSnippetsList();

        return results;
    }

    /**
     * 检查数据完整性
     * @SERVICE 数据完整性检查服务
     */
    validateDataIntegrity() {
        const issues = [];

        // 检查重复的渠道/字段组合
        const channelFieldMap = new Map();
        this.promptSnippets.forEach(snippet => {
            const key = `${snippet.channel}/${snippet.field}`;
            if (channelFieldMap.has(key)) {
                issues.push({
                    type: 'duplicate',
                    message: `重复的渠道/字段组合: ${key}`,
                    snippets: [channelFieldMap.get(key), snippet.id]
                });
            } else {
                channelFieldMap.set(key, snippet.id);
            }
        });

        // 检查必填字段
        this.promptSnippets.forEach(snippet => {
            if (!snippet.name || !snippet.template || !snippet.channel || !snippet.field) {
                issues.push({
                    type: 'missing_required',
                    message: `片段 ${snippet.id} 缺少必填字段`,
                    snippet: snippet.id
                });
            }
        });

        return {
            valid: issues.length === 0,
            issues: issues
        };
    }
}

class AIOptimizer {
    constructor(promptEditor, geminiService) {
        this.promptEditor = promptEditor;
        this.geminiService = geminiService;
        this.optimizationHistory = []; // 优化历史记录
    }

    /**
     * 分析订单内容，识别需要优化的字段
     * @SERVICE AI字段分析服务
     * @param {string} orderContent - 订单内容
     * @returns {Array} 需要优化的字段列表
     */
    analyzeRequiredFields(orderContent) {
        try {
            const fieldMapper = this.promptEditor.fieldMapper;
            const requiredFields = fieldMapper.getApiFieldDefinitions().required || [];
            const optionalFields = fieldMapper.getApiFieldDefinitions().optional || [];

            // 基于订单内容分析哪些字段需要优化
            const analysisResults = [];

            [...requiredFields, ...optionalFields].forEach(field => {
                const fieldInfo = fieldMapper.getApiFieldDefinitions().descriptions[field];
                if (fieldInfo) {
                    // 检查订单内容中是否包含该字段相关信息
                    const relevanceScore = this.calculateFieldRelevance(orderContent, field, fieldInfo);

                    if (relevanceScore > 0.3) { // 相关性阈值
                        analysisResults.push({
                            field: field,
                            relevance: relevanceScore,
                            description: fieldInfo.description,
                            currentPrompt: this.getCurrentPromptForField(field),
                            priority: this.calculateOptimizationPriority(field, relevanceScore)
                        });
                    }
                }
            });

            // 按优先级排序
            analysisResults.sort((a, b) => b.priority - a.priority);

            console.log('📊 字段分析结果:', analysisResults);
            return analysisResults;

        } catch (error) {
            console.error('❌ 字段分析失败:', error);
            return [];
        }
    }

    /**
     * 计算字段与订单内容的相关性
     * @UTIL 相关性计算工具
     */
    calculateFieldRelevance(orderContent, field, fieldInfo) {
        const content = orderContent.toLowerCase();
        let score = 0;

        // 基于字段名匹配
        if (content.includes(field.toLowerCase())) {
            score += 0.5;
        }

        // 基于字段描述中的关键词匹配
        if (fieldInfo.keywords) {
            fieldInfo.keywords.forEach(keyword => {
                if (content.includes(keyword.toLowerCase())) {
                    score += 0.2;
                }
            });
        }

        // 基于字段类型的启发式规则
        const fieldTypeRules = {
            'customer': ['客户', '姓名', '联系人', 'name'],
            'phone': ['电话', '手机', '联系', 'phone', 'tel'],
            'email': ['邮箱', '邮件', 'email', '@'],
            'address': ['地址', '位置', '地点', 'address'],
            'date': ['日期', '时间', '预约', 'date'],
            'channel': ['平台', '渠道', '来源', 'platform']
        };

        const keywords = fieldTypeRules[field] || [];
        keywords.forEach(keyword => {
            if (content.includes(keyword)) {
                score += 0.15;
            }
        });

        return Math.min(score, 1.0); // 限制最大值为1.0
    }

    /**
     * 获取字段当前使用的提示词
     * @UTIL 提示词获取工具
     */
    getCurrentPromptForField(field) {
        const snippets = this.promptEditor.promptSnippets;

        // 查找匹配的片段
        const matchingSnippet = snippets.find(snippet =>
            snippet.field === field ||
            snippet.usage === field ||
            snippet.name.toLowerCase().includes(field.toLowerCase())
        );

        return matchingSnippet ? matchingSnippet.template : null;
    }

    /**
     * 计算优化优先级
     * @UTIL 优先级计算工具
     */
    calculateOptimizationPriority(field, relevance) {
        // 基础优先级基于相关性
        let priority = relevance;

        // 核心字段加权
        const coreFields = ['customer', 'phone', 'email', 'address'];
        if (coreFields.includes(field)) {
            priority += 0.3;
        }

        // 检测类字段加权
        if (field === 'channel' || field.includes('detect')) {
            priority += 0.2;
        }

        return Math.min(priority, 1.0);
    }

    /**
     * 优化单个字段的提示词
     * @SERVICE AI提示词优化服务
     * @param {string} field - 字段名
     * @param {string} orderContent - 订单内容
     * @param {string} currentPrompt - 当前提示词
     * @returns {Object} 优化结果
     */
    async optimizeFieldPrompt(field, orderContent, currentPrompt = null) {
        try {
            console.log(`🤖 开始优化字段: ${field}`);

            // 获取当前提示词
            if (!currentPrompt) {
                currentPrompt = this.getCurrentPromptForField(field) || this.getDefaultPromptTemplate(field);
            }

            // 构建优化提示词
            const optimizationPrompt = this.buildOptimizationPrompt(field, orderContent, currentPrompt);

            // 调用Gemini API
            const response = await this.geminiService.generateContent(optimizationPrompt);

            if (response.success && response.content) {
                const optimizationResult = this.parseOptimizationResponse(response.content, field, currentPrompt);

                // 记录优化历史
                this.recordOptimization(field, currentPrompt, optimizationResult);

                console.log(`✅ 字段 ${field} 优化完成`);
                return optimizationResult;
            } else {
                throw new Error(response.error || 'AI优化失败');
            }

        } catch (error) {
            console.error(`❌ 优化字段 ${field} 失败:`, error);

            // 返回模拟优化结果作为降级方案
            return this.getMockOptimizationResult(field, currentPrompt, orderContent);
        }
    }

    /**
     * 构建优化提示词
     * @UTIL 优化提示词构建工具
     */
    buildOptimizationPrompt(field, orderContent, currentPrompt) {
        return `你是提示词优化专家。请基于以下订单内容优化字段提取提示词:

订单内容:
${orderContent}

目标字段: ${field}
当前提示词: ${currentPrompt}

请提供:
1. 优化后的提示词 (保持简洁清晰，适合AI理解)
2. 主要改进点说明 (3-5个要点)
3. 预期准确率提升百分比 (0-50%)

要求:
- 提高提取准确率，减少误判和遗漏
- 适配订单内容特征和语言风格
- 保持提示词简洁性，避免冗余
- 使用清晰的指令格式

请以JSON格式返回:
{
  "optimizedPrompt": "优化后的提示词",
  "improvements": ["改进点1", "改进点2", "改进点3"],
  "expectedImprovement": 数字,
  "reasoning": "优化理由说明"
}`;
    }

    /**
     * 解析AI优化响应
     * @UTIL 响应解析工具
     */
    parseOptimizationResponse(content, field, originalPrompt) {
        try {
            // 尝试解析JSON响应
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const result = JSON.parse(jsonMatch[0]);
                return {
                    field: field,
                    originalPrompt: originalPrompt,
                    optimizedPrompt: result.optimizedPrompt,
                    improvements: result.improvements || [],
                    expectedImprovement: result.expectedImprovement || 0,
                    reasoning: result.reasoning || '',
                    timestamp: new Date().toISOString()
                };
            }
        } catch (error) {
            console.warn('解析AI响应失败，使用文本解析:', error);
        }

        // 降级到文本解析
        return {
            field: field,
            originalPrompt: originalPrompt,
            optimizedPrompt: content.substring(0, 500), // 截取前500字符作为优化结果
            improvements: ['AI响应解析失败，请手动检查优化内容'],
            expectedImprovement: 10,
            reasoning: '基于AI文本响应的优化建议',
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 获取默认提示词模板
     * @UTIL 默认模板工具
     */
    getDefaultPromptTemplate(field) {
        const templates = {
            'customer': '请从文本中提取客户姓名信息',
            'phone': '请从文本中提取电话号码信息',
            'email': '请从文本中提取邮箱地址信息',
            'address': '请从文本中提取地址信息',
            'date': '请从文本中提取日期时间信息',
            'channel': '请识别文本来源平台或渠道'
        };

        return templates[field] || `请从文本中提取${field}相关信息`;
    }

    /**
     * 记录优化历史
     * @SERVICE 优化历史记录服务
     */
    recordOptimization(field, originalPrompt, optimizationResult) {
        this.optimizationHistory.push({
            field: field,
            originalPrompt: originalPrompt,
            optimizationResult: optimizationResult,
            timestamp: new Date().toISOString()
        });

        // 保持最近50条记录
        if (this.optimizationHistory.length > 50) {
            this.optimizationHistory = this.optimizationHistory.slice(-50);
        }
    }

    /**
     * 获取模拟优化结果（降级方案）
     * @UTIL 模拟数据工具
     */
    getMockOptimizationResult(field, currentPrompt, orderContent) {
        const mockImprovements = [
            '增加了上下文理解能力',
            '优化了字段识别准确性',
            '改进了多语言支持',
            '增强了边界情况处理'
        ];

        const mockOptimizedPrompt = currentPrompt ?
            `${currentPrompt}\n\n请特别注意从以下类型的内容中准确提取信息，确保结果的完整性和准确性。` :
            this.getDefaultPromptTemplate(field) + '\n\n请确保提取结果准确完整。';

        return {
            field: field,
            originalPrompt: currentPrompt,
            optimizedPrompt: mockOptimizedPrompt,
            improvements: mockImprovements.slice(0, 3),
            expectedImprovement: Math.floor(Math.random() * 20) + 10, // 10-30%
            reasoning: '基于字段特征和内容分析的优化建议（模拟结果）',
            timestamp: new Date().toISOString(),
            isMock: true
        };
    }

    /**
     * 批量优化提示词
     * @SERVICE 批量优化服务
     * @param {string} orderContent - 订单内容
     * @returns {Object} 批量优化结果
     */
    async optimizePromptsForOrder(orderContent) {
        try {
            console.log('🚀 开始批量优化提示词...');

            // 1. 分析需要优化的字段
            const fieldsToOptimize = this.analyzeRequiredFields(orderContent);

            if (fieldsToOptimize.length === 0) {
                return {
                    success: true,
                    message: '未发现需要优化的字段',
                    results: []
                };
            }

            // 2. 显示进度界面
            this.showOptimizationProgress(fieldsToOptimize);

            // 3. 批量执行优化
            const optimizationResults = [];

            for (let i = 0; i < fieldsToOptimize.length; i++) {
                const fieldInfo = fieldsToOptimize[i];

                try {
                    // 更新进度
                    this.updateOptimizationProgress(i, fieldsToOptimize.length, `正在优化: ${fieldInfo.field}`);

                    // 执行优化
                    const result = await this.optimizeFieldPrompt(
                        fieldInfo.field,
                        orderContent,
                        fieldInfo.currentPrompt
                    );

                    optimizationResults.push({
                        ...result,
                        success: true,
                        fieldInfo: fieldInfo
                    });

                    // 短暂延迟，避免API限制
                    await this.delay(500);

                } catch (error) {
                    console.error(`优化字段 ${fieldInfo.field} 失败:`, error);
                    optimizationResults.push({
                        field: fieldInfo.field,
                        success: false,
                        error: error.message,
                        fieldInfo: fieldInfo
                    });
                }
            }

            // 4. 完成进度显示
            this.updateOptimizationProgress(fieldsToOptimize.length, fieldsToOptimize.length, '优化完成！');

            // 5. 显示结果
            setTimeout(() => {
                this.showOptimizationResults(optimizationResults);
            }, 1000);

            console.log('✅ 批量优化完成:', optimizationResults);

            return {
                success: true,
                results: optimizationResults,
                totalFields: fieldsToOptimize.length,
                successCount: optimizationResults.filter(r => r.success).length,
                failureCount: optimizationResults.filter(r => !r.success).length
            };

        } catch (error) {
            console.error('❌ 批量优化失败:', error);
            this.hideOptimizationProgress();

            return {
                success: false,
                error: error.message,
                results: []
            };
        }
    }

    /**
     * 显示优化进度界面
     * @SERVICE 进度显示服务
     */
    showOptimizationProgress(fieldsToOptimize) {
        const aiContent = document.getElementById('pe-ai-content');
        if (!aiContent) return;

        aiContent.innerHTML = `
            <div class="optimization-progress">
                <h4>🤖 AI 批量优化进行中...</h4>
                <div style="margin: 20px 0;">
                    <div class="progress-bar-container" style="background: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                        <div class="progress-bar" id="optimization-progress-bar" style="background: linear-gradient(90deg, #6f42c1, #8e44ad); height: 100%; width: 0%; transition: width 0.3s;"></div>
                    </div>
                    <div class="progress-text" id="optimization-progress-text" style="margin-top: 10px; font-size: 14px; color: #666;">
                        准备开始优化 ${fieldsToOptimize.length} 个字段...
                    </div>
                </div>
                <div class="fields-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px;">
                    ${fieldsToOptimize.map(field => `
                        <div class="field-item" id="field-${field.field}" style="padding: 8px; margin: 4px 0; background: #f8f9fa; border-radius: 4px; display: flex; justify-content: space-between; align-items: center;">
                            <span>${field.field}</span>
                            <span class="field-status" style="font-size: 12px; color: #666;">等待中...</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 更新优化进度
     * @SERVICE 进度更新服务
     */
    updateOptimizationProgress(current, total, message) {
        const progressBar = document.getElementById('optimization-progress-bar');
        const progressText = document.getElementById('optimization-progress-text');

        if (progressBar) {
            const percentage = (current / total) * 100;
            progressBar.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${message} (${current}/${total})`;
        }

        // 更新字段状态
        if (current > 0) {
            const fieldsToOptimize = this.promptEditor.aiOptimizer.optimizationHistory.slice(-total);
            if (fieldsToOptimize[current - 1]) {
                const fieldElement = document.getElementById(`field-${fieldsToOptimize[current - 1].field}`);
                if (fieldElement) {
                    const statusElement = fieldElement.querySelector('.field-status');
                    if (statusElement) {
                        statusElement.textContent = '✅ 完成';
                        statusElement.style.color = '#28a745';
                    }
                }
            }
        }
    }

    /**
     * 隐藏优化进度
     * @SERVICE 进度隐藏服务
     */
    hideOptimizationProgress() {
        const aiContent = document.getElementById('pe-ai-content');
        if (aiContent) {
            aiContent.innerHTML = `
                <div style="text-align: center; color: #666; padding: 40px 20px;">
                    <p>选择一个片段后，点击"分析优化"获取AI建议</p>
                </div>
            `;
        }
    }

    /**
     * 显示优化结果
     * @SERVICE 结果显示服务
     */
    showOptimizationResults(results) {
        const aiContent = document.getElementById('pe-ai-content');
        if (!aiContent) return;

        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;

        aiContent.innerHTML = `
            <div class="optimization-results">
                <h4>🎉 优化完成</h4>
                <div class="results-summary" style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>总计字段:</span>
                        <span><strong>${totalCount}</strong></span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>成功优化:</span>
                        <span style="color: #28a745;"><strong>${successCount}</strong></span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>失败:</span>
                        <span style="color: #dc3545;"><strong>${totalCount - successCount}</strong></span>
                    </div>
                </div>

                <div class="results-list" style="max-height: 300px; overflow-y: auto;">
                    ${results.map(result => `
                        <div class="result-item" style="margin: 10px 0; padding: 12px; border: 1px solid #e9ecef; border-radius: 6px; ${result.success ? 'border-left: 4px solid #28a745;' : 'border-left: 4px solid #dc3545;'}">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>${result.field}</strong>
                                <span style="font-size: 12px; color: ${result.success ? '#28a745' : '#dc3545'};">
                                    ${result.success ? '✅ 成功' : '❌ 失败'}
                                </span>
                            </div>
                            ${result.success ? `
                                <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                                    预期提升: +${result.expectedImprovement}%
                                </div>
                                <div style="font-size: 12px;">
                                    <strong>改进点:</strong> ${result.improvements.slice(0, 2).join(', ')}
                                </div>
                                <div style="margin-top: 8px;">
                                    <button class="btn btn-sm btn-primary" onclick="promptEditor.showVersionComparison('${result.field}', \`${result.originalPrompt}\`, \`${result.optimizedPrompt}\`)">
                                        查看对比
                                    </button>
                                </div>
                            ` : `
                                <div style="font-size: 12px; color: #dc3545;">
                                    错误: ${result.error}
                                </div>
                            `}
                        </div>
                    `).join('')}
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-primary" onclick="promptEditor.aiOptimizer.hideOptimizationProgress()">
                        关闭结果
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 延迟工具函数
     * @UTIL 延迟工具
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 模块工厂函数
function createPromptEditorModule(container) {
    const configManager = container.get('config');
    const fieldMapper = container.get('fieldMapper');
    const promptComposer = container.get('promptComposer');
    const localStorageManager = container.get('localStorageManager');
    const geminiService = container.get('gemini');
    return new PromptEditor(configManager, fieldMapper, promptComposer, localStorageManager, geminiService);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('promptEditor', createPromptEditorModule, ['config', 'fieldMapper', 'promptComposer', 'localStorageManager', 'gemini']);
    console.log('📦 PromptEditor 已注册到模块容器');
}
