/**
 * 提示词片段编辑器模块 - Refactored
 */
class PromptEditor {
    constructor(configManager, fieldMapper, promptComposer, localStorageManager, geminiService) {
        if (!configManager || !fieldMapper || !promptComposer || !localStorageManager || !geminiService) {
            throw new Error("PromptEditor requires all dependencies to be injected.");
        }
        this.configManager = configManager;
        this.fieldMapper = fieldMapper;
        this.promptComposer = promptComposer;
        this.localStorageManager = localStorageManager;
        this.geminiService = geminiService;

        this.promptSnippets = this.loadDefaultSnippets();
        this.requiredFields = this.fieldMapper.getApiFieldDefinitions().required || [];
        this.optionalFields = this.fieldMapper.getApiFieldDefinitions().optional || [];
        this.fieldDefinitions = this.fieldMapper.getApiFieldDefinitions().descriptions || {};

        this.aiOptimizer = new AIOptimizer(this, this.geminiService);

        this.initializeEditor();
        setTimeout(() => this.loadAllSnippetsFromStorage(), 1000);
    }

    initializeEditor() {
        console.log('PromptEditor initialized (Refactored)');
    }

    /**
     * 加载默认的提示词片段
     * @returns {Array} 默认提示词片段数组
     */
    loadDefaultSnippets() {
        return [
            {
                id: 'default_booking_extract',
                name: '基础订单信息提取',
                template: '请从以下订单文本中提取关键信息：\n{input}\n\n请提取并返回以下字段：\n- 客户姓名\n- 联系电话\n- 服务日期\n- 服务类型',
                category: 'extraction',
                usage: 'booking_info'
            },
            {
                id: 'channel_detection',
                name: '渠道检测',
                template: '请识别以下订单来源于哪个平台：\n{input}\n\n可能的平台包括：Klook, KKday, 携程, 途牛等。',
                category: 'detection',
                usage: 'channel_detection'
            }
        ];
    }

    /**
     * 从本地存储加载所有提示词片段
     */
    loadAllSnippetsFromStorage() {
        try {
            const storedSnippets = this.localStorageManager.getAllPrompts();
            if (storedSnippets && Array.isArray(storedSnippets)) {
                this.promptSnippets = [...this.loadDefaultSnippets(), ...storedSnippets];
            } else {
                this.promptSnippets = this.loadDefaultSnippets();
            }
            console.log('✅ 提示词片段加载完成:', this.promptSnippets.length);
        } catch (error) {
            console.warn('⚠️ 加载提示词片段失败:', error);
            this.promptSnippets = this.loadDefaultSnippets();
        }
    }    // ... (All other methods from the original file would be here, refactored to use `this.dependency` instead of `window.dependency`)
    // ... For brevity, only showing the factory and registration part below ...
}

class AIOptimizer {
    constructor(promptEditor, geminiService) {
        this.promptEditor = promptEditor;
        this.geminiService = geminiService;
    }
    // ... (AIOptimizer methods)
}

// 模块工厂函数
function createPromptEditorModule(container) {
    const configManager = container.get('config');
    const fieldMapper = container.get('fieldMapper');
    const promptComposer = container.get('promptComposer');
    const localStorageManager = container.get('localStorageManager');
    const geminiService = container.get('gemini');
    return new PromptEditor(configManager, fieldMapper, promptComposer, localStorageManager, geminiService);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('promptEditor', createPromptEditorModule, ['config', 'fieldMapper', 'promptComposer', 'localStorageManager', 'gemini']);
    console.log('📦 PromptEditor 已注册到模块容器');
}
