# 渠道检测编辑器 - 独立项目

## 📋 项目概述

这是一个独立的渠道检测和提示词编辑工具，基于原系统的核心功能提取和简化而来。项目包含完整的渠道检测、字段映射、规则编辑和提示词管理功能。

## 🚀 功能特性

### 核心功能
- **输入内容解析**: 自动从文本中提取订单信息并映射到表单字段
- **智能渠道检测**: 基于规则的多渠道自动检测（飞猪、携程、KKday、Klook等）
- **字段模块化架构**: 完整的字段模块化管理，支持渠道独立存储和通用模板回退
- **实时预览**: 输入时实时显示渠道检测结果和字段映射
- **🤖 AI智能优化**: 使用Gemini-2.5-Pro进行提示词优化分析

### 编辑功能
- **规则编辑器**: 可视化编辑渠道检测规则（正则表达式、置信度等）
- **字段模块化编辑器**: 支持按渠道和字段的独立提示词片段管理
- **通用模板系统**: 一键应用标准化字段模板，提升编辑效率
- **AI优化工作流**: 完整的订单分析→建议预览→用户确认→字段回填流程
- **导入导出**: 支持规则和提示词的JSON格式导入导出

### 数据支持
- **车型配置**: 智能车型推荐系统
- **API字段**: 完整的GoMyHire API字段定义
- **默认值**: 智能默认值设置和数据类型转换

## 📁 项目结构

```
plain/channel-detection-editor/
├── index.html          # 主页面
├── channel-detector.js # 渠道检测器
├── field-mapper.js     # 字段映射器
├── rule-editor.js      # 规则编辑器
├── prompt-editor.js    # 提示词编辑器
├── app.js             # 主应用程序逻辑
└── README.md          # 项目说明
```

## 🛠️ 技术架构

### 渠道检测器 (ChannelDetector)
基于正则表达式和模式匹配的多层次检测：
1. **Fliggy渠道**: 19位订单编号模式
2. **JingGe渠道**: 关键词匹配
3. **参考号模式**: CD/CT/KL/KK等前缀检测
4. **关键词检测**: 渠道名称直接匹配

### 字段映射器 (FieldMapper)
完整的字段处理流水线：
1. **文本提取**: 使用正则表达式从文本提取字段
2. **数据类型转换**: 自动转换数值、布尔值等
3. **默认值应用**: 智能默认值设置
4. **验证检查**: 必填字段验证和完整性检查

### 规则编辑器 (RuleEditor)
可视化规则管理：
- 渠道规则增删改查
- 正则表达式编辑
- 置信度调整
- 实时测试功能

### 提示词编辑器 (PromptEditor)
多渠道提示词管理：
- 字段级提示词片段
- 渠道专属处理规则
- 导入导出功能

## 🎯 使用场景

### 1. 订单内容解析
```javascript
// 输入订单文本
const inputText = `订单编号：1234567890123456789
客户姓名：张三
联系电话：+60123456789
接机地点：KLIA2 Terminal`;

// 自动处理
const result = window.fieldMapper.processCompleteData(inputText);
const channel = window.channelDetector.detectChannel(inputText);
```

### 2. 规则管理
```javascript
// 编辑检测规则
window.ruleEditor.openEditor();

// 添加新渠道
window.channelDetector.addChannelRule('NewChannel', [
    '/new-pattern/',
    '/another-pattern/'
], 0.85);
```

### 3. 提示词管理
```javascript
// 管理提示词片段
window.promptEditor.openEditor();

// 获取渠道提示词
const snippets = window.promptEditor.getSnippetsForChannel('fliggy');
```

## 🔧 安装和使用

### 快速开始
1. 直接在浏览器中打开 `index.html`
2. 在输入框中粘贴订单内容
3. 点击"处理输入"按钮查看结果
4. 注：API 配置（例如 Gemini API 密钥）已改为硬编码，界面中的“API设置”入口已移除，无需手动配置。

### 高级功能
1. 点击"编辑规则"按钮管理渠道检测规则
2. 点击"编辑提示词"按钮管理字段提示词
3. 使用导入导出功能备份和恢复配置

## 📊 数据格式

### 渠道检测结果
```json
{
    "channel": "fliggy",
    "confidence": 0.95,
    "method": "fliggy_pattern",
    "matchedPattern": "订单编号+19位数字"
}
```

### 字段映射结果
```json
{
    "data": {
        "customer_name": "张三",
        "customer_contact": "+60123456789",
        "pickup": "KLIA2 Terminal",
        "sub_category_id": 2,
        "car_type_id": 5
    },
    "validation": {
        "isValid": true,
        "missingFields": []
    }
}
```

## 🎨 界面功能

### 主界面
- **输入区域**: 支持多行文本输入，实时检测
- **结果展示**: 结构化显示提取的字段和验证结果
- **渠道检测**: 实时显示检测到的渠道和置信度
- **字段映射**: 网格布局展示映射后的字段值

### 编辑界面
- **模态窗口**: 浮层式编辑界面，不打断主流程
- **实时预览**: 编辑时实时预览效果
- **测试功能**: 内置规则测试工具
- **导入导出**: 完整的配置管理功能

## 🔮 扩展能力

### 自定义渠道
通过规则编辑器可以轻松添加新的检测渠道：
1. 定义渠道名称和标识符
2. 设置检测模式（正则表达式）
3. 配置置信度阈值
4. 添加专属提示词片段

### 字段扩展
支持自定义字段映射规则：
1. 添加新的字段提取模式
2. 定义字段处理逻辑
3. 设置字段验证规则
4. 配置默认值和类型转换

## 📝 开发说明

### 代码结构
- **模块化设计**: 每个功能独立为单独的类
- **事件驱动**: 基于事件的消息传递机制
- **响应式界面**: 支持桌面和移动设备

### 数据流
```
输入文本 → 渠道检测 → 字段提取 → 数据处理 → 结果展示
      ↓          ↓           ↓           ↓
  实时反馈    规则管理    提示词管理    验证检查
```

### 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🚀 使用指南

### 基本使用
1. **输入订单内容**：在主界面的文本框中粘贴或输入订单信息
2. **点击处理输入**：点击"处理输入"按钮进行自动解析
3. **查看结果**：系统将自动识别渠道并提取字段信息

### 🔧 提示词片段编辑器（字段模块化）
1. **打开编辑器**：点击"📝 编辑提示词"按钮
2. **选择渠道**：从下拉菜单选择要编辑的渠道（支持渠道独立存储）
3. **选择字段**：使用字段选择器选择要编辑的字段
4. **编辑片段**：为每个字段编写专门的提示词片段
5. **应用模板**：点击"📋 应用模板"使用通用字段模板
6. **保存设置**：点击"💾 保存所有字段"进行持久化

### 🤖 AI优化功能
1. **输入订单内容**：在编辑器的"订单内容输入"区域粘贴订单信息
2. **AI分析**：点击"🚀 AI生成字段建议"使用Gemini-2.5-Pro进行分析
3. **预览建议**：查看AI生成的字段优化建议和理由说明
4. **选择应用**：勾选要应用的建议，支持全选/仅必填等快捷操作
5. **确认回填**：点击"✅ 应用选中建议"将优化结果填入对应字段

### 🔍 系统验证
- **状态检查**：点击"🔍 系统状态"查看字段模块化功能状态
- **功能测试**：点击"🧪 测试组合"验证字段模块化组合效果
- **开发者工具**：在控制台运行`validateFieldModularSystem()`进行完整验证

### AI模型配置
- **默认模型**：使用`gemini-2.5-flash`进行智能文本处理
- **优化模型**：使用`gemini-2.5-pro`进行提示词优化分析（仅在AI优化功能中使用）
- **渠道回退**：当具体渠道缺少字段片段时，自动使用通用渠道(generic)作为默认值

## 🆘 技术支持

如有问题请提交 Issue 或联系开发团队。