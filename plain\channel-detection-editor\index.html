<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道检测编辑器 - 独立项目</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        /* 提示词编辑器模态框样式 */
        .prompt-editor-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .prompt-editor-content {
            background: white;
            border-radius: 12px;
            width: 95%;
            max-width: 1400px;
            height: 85vh;
            max-height: 800px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .prompt-editor-header {
            background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .prompt-editor-header h2 {
            font-size: 1.5em;
            font-weight: 500;
        }

        .prompt-editor-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .prompt-editor-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .prompt-editor-body {
            flex: 1;
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            gap: 0;
            overflow: hidden;
        }

        /* 左侧片段列表 */
        .prompt-snippets-panel {
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .snippets-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: white;
        }

        .snippets-search {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .snippets-filters {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .filter-tag {
            padding: 4px 8px;
            background: #e9ecef;
            border: none;
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-tag.active {
            background: #6f42c1;
            color: white;
        }

        .snippets-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .snippet-item {
            padding: 12px;
            margin-bottom: 8px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .snippet-item:hover {
            border-color: #6f42c1;
            box-shadow: 0 2px 8px rgba(111, 66, 193, 0.1);
        }

        .snippet-item.active {
            border-color: #6f42c1;
            background: #f8f6ff;
        }

        .snippet-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .snippet-meta {
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
        }

        /* 中间编辑区域 */
        .prompt-editor-panel {
            background: white;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #e9ecef;
        }

        .editor-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .editor-form {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #6f42c1;
            box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
        }

        .form-textarea {
            min-height: 200px;
            resize: vertical;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .form-actions {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
            display: flex;
            gap: 10px;
        }

        /* 右侧AI建议区 */
        .ai-suggestions-panel {
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .ai-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: white;
        }

        .ai-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #6f42c1;
            color: white;
        }

        .btn-primary:hover {
            background: #5a2d91;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }

        .input-section textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Monaco', 'Menlo', monospace;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .input-section textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .result-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow: auto;
            border: 1px solid #e9ecef;
        }

        .result-item {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 6px;
            background: #f8f9fa;
            border-left: 4px solid #4facfe;
        }

        .result-item.success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .result-item.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .result-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .field-mapping {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .field-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .field-card h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .field-card p {
            color: #6c757d;
            font-size: 13px;
            margin: 0;
        }

        .field-value {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            margin-top: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #495057;
            border: 1px solid #e9ecef;
            min-height: 20px;
        }

        .field-value:not(:empty) {
            background: #e8f5e8;
            border-color: #28a745;
            color: #155724;
        }

        .channel-detection {
            margin-top: 20px;
        }

        .detection-result {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #4facfe;
            margin-top: 15px;
        }

        .detection-result h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .detection-details {
            font-size: 13px;
            color: #495057;
        }

        .confidence-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .field-mapping {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 渠道检测编辑器</h1>
            <p>独立项目 - 输入内容映射到表单 + 渠道检测特征编辑</p>
        </div>

        <div class="main-content">
            <div class="section input-section">
            <h2>📝 输入内容</h2>
            <textarea id="inputContent" placeholder="请输入订单内容..."></textarea>
                
                <div class="button-group">
                    <button class="btn-primary" id="processInputBtn">处理输入</button>
                    <button class="btn-secondary" id="clearInputBtn">清空</button>
                    <button class="btn-secondary" id="editRulesBtn" style="background: #fd7e14;">🛠️ 编辑规则</button>
                    <button class="btn-secondary" id="editPromptBtn" style="background: #6f42c1;">📝 编辑提示词</button>
                </div>
            </div>

            <div class="section">
                <h2>📊 处理结果</h2>
                <div class="result-section" id="resultContainer">
                    <div class="result-item">
                        <strong>等待处理...</strong>
                        <p>请输入内容并点击"处理输入"按钮</p>
                    </div>
                </div>

                <div class="channel-detection">
                    <h3>🔍 渠道检测结果</h3>
                    <div class="detection-result" id="channelResult">
                        <h4>未检测</h4>
                        <p>等待渠道检测...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section" style="grid-column: 1 / -1; margin: 30px;">
            <h2>🗂️ 字段映射展示</h2>
            <div class="field-mapping" id="fieldMapping">
                <div class="field-card">
                    <h4>customer_name</h4>
                    <p>客户姓名</p>
                    <div class="field-value" id="customer_name_value">-</div>
                </div>
                <div class="field-card">
                    <h4>customer_contact</h4>
                    <p>客户联系电话</p>
                    <div class="field-value" id="customer_contact_value">-</div>
                </div>
                <div class="field-card">
                    <h4>customer_email</h4>
                    <p>客户邮箱</p>
                    <div class="field-value" id="customer_email_value">-</div>
                </div>
                <div class="field-card">
                    <h4>ota_reference_number</h4>
                    <p>OTA参考编号</p>
                    <div class="field-value" id="ota_reference_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>flight_info</h4>
                    <p>航班信息</p>
                    <div class="field-value" id="flight_info_value">-</div>
                </div>
                <div class="field-card">
                    <h4>pickup</h4>
                    <p>接客地点</p>
                    <div class="field-value" id="pickup_value">-</div>
                </div>
                <div class="field-card">
                    <h4>destination</h4>
                    <p>目的地</p>
                    <div class="field-value" id="destination_value">-</div>
                </div>
                <div class="field-card">
                    <h4>passenger_number</h4>
                    <p>乘客数量</p>
                    <div class="field-value" id="passenger_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>luggage_number</h4>
                    <p>行李数量</p>
                    <div class="field-value" id="luggage_number_value">-</div>
                </div>
                <div class="field-card">
                    <h4>ota_price</h4>
                    <p>OTA平台价格</p>
                    <div class="field-value" id="ota_price_value">-</div>
                </div>
                <div class="field-card">
                    <h4>sub_category_id</h4>
                    <p>服务类型ID (2:接机, 3:送机, 4:包车)</p>
                    <div class="field-value" id="sub_category_id_value">-</div>
                </div>
                <div class="field-card">
                    <h4>extra_requirement</h4>
                    <p>额外要求/备注信息</p>
                    <div class="field-value" id="extra_requirement_value">-</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Refactored Script Loading Order -->
    <!-- 1. Module Container (Must be first) -->
    <script src="module-container.js"></script>

    <!-- 2. Core Services & Utils -->
    <script src="error-handler.js"></script>
    <script src="local-storage-manager.js"></script>
    <script src="crypto-utils.js"></script>
    <script src="../hotels_by_region.js"></script>
    <script src="airport-data.js"></script>

    <!-- 3. Unified Config -->
    <script src="config.js"></script>

    <!-- 4. Core Business Logic Modules -->
    <script src="gemini-config.js"></script>
    <script src="channel-detector.js"></script>
    <script src="address-translator.js"></script>
    <script src="prompt-segmenter.js"></script>
    <script src="prompt-composer.js"></script>
    <script src="prompt-fragments.js"></script>
    <script src="field-mapper.js"></script>

    <!-- 5. UI/Editor Modules -->
    <script src="rule-editor.js"></script>
    <script src="prompt-editor.js"></script>

    <!-- 6. Cache System -->
    <script src="cache-manager.js"></script>
    <script src="cache-integration-adapter.js"></script>
    <script src="cache-monitor-panel.js"></script>

    <!-- 7. Main Application Logic -->
    <script src="app.js"></script>

    <!-- 8. Unified Initializer (Must be last) -->
    <script src="main.js"></script>
</body>
</html>