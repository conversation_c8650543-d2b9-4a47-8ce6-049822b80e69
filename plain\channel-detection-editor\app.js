/**
 * 主应用程序模块 - Refactored
 */
class ChannelDetectionEditor {
    constructor(container) {
        if (!container) {
            throw new Error("Module container is a required dependency.");
        }
        this.container = container;
        this.services = {}; // Service cache
        this.autoAnalyzing = false;
        this.initialize();
    }

    initialize() {
        console.log('渠道检测编辑器应用已初始化 (Refactored)');
        this.bindEvents();
    }

    getService(serviceName) {
        if (this.services[serviceName]) {
            return this.services[serviceName];
        }
        const service = this.container.get(serviceName);
        if (!service) {
            throw new Error(`Service not found in container: ${serviceName}`);
        }
        this.services[serviceName] = service;
        return service;
    }

    bindEvents() {
        const inputTextarea = document.getElementById('inputContent');
        if(inputTextarea) {
            inputTextarea.addEventListener('input', (e) => {
                this.debounce(() => {
                    this.autoDetectFeatures(e.target.value);
                }, 50)();
            });

            inputTextarea.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    this.processInput();
                }
            });
        }

        // Bind buttons
        document.getElementById('processInputBtn')?.addEventListener('click', () => this.processInput());
        document.getElementById('clearInputBtn')?.addEventListener('click', () => this.clearInput());
        document.getElementById('editRulesBtn')?.addEventListener('click', () => this.editDetectionRules());
        document.getElementById('editPromptBtn')?.addEventListener('click', () => this.editPromptSnippets());
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    async processInput() {
        const inputText = document.getElementById('inputContent').value.trim();
        if (!inputText) {
            this.showResult('请输入内容后再处理', 'warning');
            return;
        }

        this.showLoading();

        try {
            const channelDetector = this.getService('channelDetector');
            const fieldMapper = this.getService('fieldMapper');

            const channelResult = channelDetector.detectChannel(inputText);
            this.displayChannelResult(channelResult);

            const processingResult = await fieldMapper.processCompleteData(inputText);
            this.displayFieldMapping(processingResult);

            this.showCompleteResult(inputText, channelResult, processingResult);

        } catch (error) {
            console.error('❌ 处理失败:', error);
            this.showResult(`处理失败: ${error.message}`, 'error');
        }
    }

    async autoDetectFeatures(text) {
        if (!text || text.length < 10 || this.autoAnalyzing) return;

        try {
            this.autoAnalyzing = true;
            const channelDetector = this.getService('channelDetector');
            const channelResult = channelDetector.detectChannel(text);
            if (channelResult.confidence > 0.3) {
                this.displayChannelResult(channelResult, true);
            }

            if (text.length > 50) {
                this.showAutoAnalysisStatus();
                const fieldMapper = this.getService('fieldMapper');
                const processingResult = await fieldMapper.processCompleteData(text);
                this.displayFieldMapping(processingResult);
                this.showCompleteResult(text, channelResult, processingResult);
            }
        } catch (error) {
            console.warn('⚠️ 自动检测失败:', error);
        } finally {
            this.autoAnalyzing = false;
        }
    }

    /**
     * 打开规则编辑器
     */
    editDetectionRules() {
        try {
            const ruleEditor = this.getService('ruleEditor');
            ruleEditor.openEditor();
        } catch (error) {
            console.error('❌ 打开规则编辑器失败:', error);
            this.showResult(`打开规则编辑器失败: ${error.message}`, 'error');
        }
    }

    /**
     * 打开提示词编辑器
     */
    editPromptSnippets() {
        try {
            const promptEditor = this.getService('promptEditor');
            if (promptEditor.openEditor) {
                promptEditor.openEditor();
            } else {
                // 如果没有 openEditor 方法，创建一个简单的编辑界面
                this.createPromptEditorModal();
            }
        } catch (error) {
            console.error('❌ 打开提示词编辑器失败:', error);
            this.showResult(`打开提示词编辑器失败: ${error.message}`, 'error');
        }
    }

    /**
     * 创建提示词编辑器模态框
     */
    createPromptEditorModal() {
        // 创建一个简单的提示词编辑界面
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
            background: rgba(0,0,0,0.7); z-index: 10000; display: flex; 
            align-items: center; justify-content: center;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            background: white; padding: 20px; border-radius: 8px; 
            max-width: 80%; max-height: 80%; overflow: auto;
        `;
        
        content.innerHTML = `
            <h2>提示词编辑器</h2>
            <p>提示词编辑器功能正在开发中...</p>
            <textarea style="width: 500px; height: 200px;" placeholder="在这里编辑提示词..."></textarea>
            <br><br>
            <button onclick="this.parentNode.parentNode.remove()">关闭</button>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
    }

    /**
     * 显示结果信息
     */
    showResult(message, type = 'info') {
        const resultContainer = document.getElementById('resultContainer');
        if (resultContainer) {
            resultContainer.innerHTML = `
                <div class="result-item ${type}">
                    <strong>${message}</strong>
                </div>
            `;
        }
    }
    
    /**
     * 清空输入
     */
    clearInput() {
        const inputTextarea = document.getElementById('inputContent');
        if (inputTextarea) {
            inputTextarea.value = '';
        }
        this.showResult('输入已清空', 'info');
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const resultContainer = document.getElementById('resultContainer');
        if (resultContainer) {
            resultContainer.innerHTML = `
                <div class="result-item info">
                    <strong>🔄 正在处理...</strong>
                </div>
            `;
        }
    }

    /**
     * 显示渠道检测结果
     */
    displayChannelResult(channelResult, isAuto = false) {
        const prefix = isAuto ? '🤖 自动检测' : '🔍 检测结果';
        console.log(`${prefix}:`, channelResult);
        
        if (channelResult && channelResult.channel) {
            this.showResult(`${prefix}: ${channelResult.channel} (置信度: ${channelResult.confidence})`, 'success');
        } else {
            this.showResult(`${prefix}: 未识别到明确渠道`, 'warning');
        }
    }

    /**
     * 显示字段映射结果
     */
    displayFieldMapping(processingResult) {
        console.log('📋 字段映射结果:', processingResult);
        
        if (processingResult) {
            this.showResult('✅ 字段映射完成', 'success');
        }
    }

    /**
     * 显示完整处理结果
     */
    showCompleteResult(inputText, channelResult, processingResult) {
        console.log('✅ 处理完成:', { 
            输入: inputText.substring(0, 100) + '...',
            渠道: channelResult,
            处理结果: processingResult
        });
        
        this.showResult('✅ 处理完成！查看控制台获取详细结果。', 'success');
    }

    /**
     * 显示自动分析状态
     */
    showAutoAnalysisStatus() {
        console.log('🤖 自动分析中...');
    }
}

// 模块工厂函数
function createApplicationModule(container) {
    return new ChannelDetectionEditor(container);
}
