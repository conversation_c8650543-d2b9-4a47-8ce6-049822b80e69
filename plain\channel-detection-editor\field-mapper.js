/**
 * 字段映射器模块 - 模块化重构版本
 * 
 * 重构更新：
 * - 消除全局变量污染 (window.fieldMapper)
 * - 实现依赖注入，显式传入Gemini服务和其他依赖
 * - 分离关注点：专注于字段映射逻辑，服务定位交给容器
 * - 保持AI增强处理的核心功能不变
 * 
 * 设计原则：
 * - 依赖注入：通过构造函数接收所需服务
 * - 服务解耦：不直接依赖全局服务实例
 * - 功能稳定：保持原有API和处理逻辑
 * - 错误隔离：依赖不可用时优雅降级
 */

class FieldMapper {
    constructor(geminiService = null, channelDetector = null, addressTranslator = null, promptFragmentManager = null) {
        // 依赖注入 - 接收外部服务
        this.geminiService = geminiService;
        this.channelDetector = channelDetector; 
        this.addressTranslator = addressTranslator;
        this.promptFragmentManager = promptFragmentManager;
        
        this.defaultValues = {};
        this.apiFieldDefinitions = {
            descriptions: {
                customer_name: '客户姓名',
                customer_contact: '客户联系电话',
                customer_email: '客户邮箱',
                ota_reference_number: 'OTA参考编号',
                flight_info: '航班信息',
                pickup: '接客地点',
                destination: '目的地',
                passenger_number: '乘客数量',
                luggage_number: '行李数量',
                ota_price: 'OTA平台价格',
                sub_category_id: '服务类型ID',
                extra_requirement: '额外要求/备注信息',
                date: '服务日期',
                time: '服务时间',
                car_type_id: '车型ID',
                languages_id_array: '语言服务数组',
                ota: '渠道名称'
            }
        };
    }

    

    validateGeminiJson(obj) {
        const errors = [];
        const normalized = { ...obj };
        const toNum = v => {
            if (v === null || v === undefined || v === '') return null;
            const n = Number(v);
            return Number.isFinite(n) ? n : null;
        };

        ['car_type_id', 'sub_category_id', 'passenger_number', 'luggage_number', 'ota_price'].forEach(k => {
            normalized[k] = normalized.hasOwnProperty(k) ? toNum(normalized[k]) : null;
        });

        ['ota', 'ota_reference_number', 'customer_name', 'customer_contact', 'customer_email', 'flight_info', 'pickup', 'destination', 'date', 'time', 'extra_requirement', 'extra_requirement_raw'].forEach(k => {
            if (!normalized.hasOwnProperty(k) || normalized[k] === undefined) normalized[k] = null;
            else if (normalized[k] !== null && typeof normalized[k] !== 'string') normalized[k] = String(normalized[k]);
        });

        if (!normalized.hasOwnProperty('languages_id_array') || normalized.languages_id_array === undefined) normalized.languages_id_array = null;
        else if (normalized.languages_id_array !== null && typeof normalized.languages_id_array === 'object') {
            const out = {}; let i = 0;
            Object.values(normalized.languages_id_array).forEach(v => { out[String(i++)] = toNum(v); });
            normalized.languages_id_array = out;
        } else if (normalized.languages_id_array !== null) {
            errors.push('languages_id_array must be object or null'); normalized.languages_id_array = null;
        }

        if (normalized.extra_requirement && normalized.extra_requirement.length > 120) normalized.extra_requirement = normalized.extra_requirement.substring(0, 120);
        if (normalized.ota === '') normalized.ota = null;

        return { valid: errors.length === 0, data: normalized, errors };
    }

    extractJsonSubstring(s) {
        const first = s.indexOf('{'); const last = s.lastIndexOf('}');
        if (first === -1 || last === -1 || last <= first) return null; return s.substring(first, last+1);
    }

    async enhanceWithGemini(text, detectedChannel) {
        // 优先使用注入的 promptFragmentManager
        if (!this.promptFragmentManager) {
            console.error('❌ PromptFragmentManager 服务不可用!');
            throw new Error('PromptFragmentManager service is not available.');
        }

        const prompt = this.promptFragmentManager.buildPrompt(text, detectedChannel);
        
        try {
            // 使用注入的Gemini服务
            if (this.geminiService && typeof this.geminiService.callGeminiAPIWithRetry === 'function') {
                const result = await this.geminiService.callGeminiAPIWithRetry(prompt);
                if (result.success && result.content) {
                    const json = this.extractJsonSubstring(String(result.content));
                    if (!json) return { __raw: result.content };
                    const parsed = JSON.parse(json);
                    const v = this.validateGeminiJson(parsed);
                    return v.valid ? v.data : { __raw: result.content };
                } else {
                    console.warn('Gemini API调用失败:', result.error);
                    return {};
                }
            } else {
                console.warn('Gemini配置不可用，无法进行AI增强');
                return {};
            }
        } catch (e) {
            console.warn('Gemini增强处理失败:', e.message);
            return {};
        }
    }
    
    

    
    
    /**
     * 获取API字段定义
     * @returns {Object} 字段定义对象
     */
    getApiFieldDefinitions() {
        return this.apiFieldDefinitions;
    }
    
    async processCompleteData(text) {
        try {
            // 步骤1: 渠道检测 (前置处理，为提示词提供上下文)
            let detectedChannel = null;
            try {
                if (this.channelDetector && typeof this.channelDetector.detectChannel === 'function') {
                    const cd = this.channelDetector.detectChannel(text);
                    if (cd && cd.channel && cd.confidence > 0.3) {
                        detectedChannel = cd.channel;
                    }
                }
            } catch (e) {
                console.warn('渠道检测失败:', e.message);
            }

            // 步骤2: 使用新的PromptFragmentManager和Gemini进行智能提取
            const gemini = await this.enhanceWithGemini(text, detectedChannel) || {};
            
            // 如果 Gemini 返回了原始内容，说明处理失败
            if (gemini.__raw) {
                return {
                    data: {},
                    geminiUsed: false,
                    validation: { isValid: false, message: 'Gemini 处理失败', missingFields: [] },
                    extractedFields: [],
                    enhancedFields: [],
                    addressTranslationUsed: false,
                    translationResults: {},
                    error: 'Gemini processing failed'
                };
            }
            
            // 步骤3: 数据验证和地址翻译
            const validationResult = this.validateGeminiJson(gemini);
            const translated = await this.safeTranslateAddresses(validationResult.data);
            
            // 创建验证信息
            const validation = {
                isValid: validationResult?.valid || false,
                message: validationResult?.valid ? '所有字段验证通过' : '字段验证发现问题',
                missingFields: Array.isArray(validationResult?.errors) ? validationResult.errors : []
            };
            
            return { 
                data: translated.data || validationResult.data || {},
                geminiUsed: true,
                validation: validation,
                extractedFields: [], // 不再使用本地提取
                enhancedFields: gemini ? Object.keys(gemini).filter(k => k !== '__raw') : [],
                addressTranslationUsed: translated.addressTranslationUsed || false,
                translationResults: translated.translationResults || {}
            };
        } catch (error) {
            console.error('processCompleteData 发生错误:', error);
            // 返回安全的默认结构
            return {
                data: {},
                geminiUsed: false,
                validation: { isValid: false, message: `处理失败: ${error.message}`, missingFields: [] },
                extractedFields: [],
                enhancedFields: [],
                addressTranslationUsed: false,
                translationResults: {},
                error: error.message
            };
        }
    }

    async safeTranslateAddresses(data) {
        try {
            return await this.translateAddresses(data);
        } catch (error) {
            console.warn('地址翻译失败:', error);
            return { data: data || {}, addressTranslationUsed: false, translationResults: {} };
        }
    }

    async translateAddresses(data) {
        if (!data) return { data };
        
        const translator = this.addressTranslator;
        
        if (!translator) {
            return { data };
        }
        
        const out = { ...data };
        const translationResults = {};
        let hasTranslations = false;
        
        try {
            // 翻译pickup地址
            if (out.pickup) {
                const pickupResult = await translator.translateAddress(out.pickup);
                translationResults.pickup = pickupResult;
                
                if (pickupResult && pickupResult.translated && pickupResult.confidence > 0.7) {
                    out.pickup = pickupResult.translated;
                    out.pickup_original = data.pickup;
                    hasTranslations = true;
                }
            }
            
            // 翻译destination地址
            if (out.destination) {
                const destinationResult = await translator.translateAddress(out.destination);
                translationResults.destination = destinationResult;
                
                if (destinationResult && destinationResult.translated && destinationResult.confidence > 0.7) {
                    out.destination = destinationResult.translated;
                    out.destination_original = data.destination;
                    hasTranslations = true;
                }
            }
        } catch (e) {
            console.warn('地址翻译失败:', e.message);
        }
        
        return { 
            data: out,
            translationResults: hasTranslations ? translationResults : {},
            addressTranslationUsed: hasTranslations
        };
    }
}

// 模块工厂函数 - Refactored
function createFieldMapperModule(container) {
    const geminiService = container.get('gemini');
    const channelDetector = container.get('channelDetector');
    const addressTranslator = container.get('addressTranslator');
    const promptFragmentManager = container.get('promptFragmentManager');
    
    return new FieldMapper(geminiService, channelDetector, addressTranslator, promptFragmentManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('fieldMapper', createFieldMapperModule, ['gemini', 'channelDetector', 'addressTranslator', 'promptFragmentManager']);
    console.log('📦 FieldMapper已注册到模块容器');
}
