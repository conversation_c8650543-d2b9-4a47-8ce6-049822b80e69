/**
 * 应用程序主入口 (Refactored)
 * 
 * 职责:
 * 1. 注册所有应用模块到模块容器。
 * 2. 定义模块加载顺序和依赖关系。
 * 3. 初始化模块容器并启动应用。
 */
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 DOM已加载，开始统一初始化流程...');

    const container = window.moduleContainer;
    if (!container) {
        console.error('❌ 模块容器未找到！初始化失败。');
        return;
    }

    try {
        // 阶段 1: 注册核心服务和配置
        container.register('config', createConfigModule, []);
        container.register('localStorageManager', createLocalStorageManagerModule, []);
        container.register('gemini', createGeminiModule, ['localStorageManager']);
        
        // 阶段 2: 注册核心业务逻辑模块
        container.register('channelDetector', createChannelDetectorModule, ['config']);
        container.register('addressTranslator', createAddressTranslatorModule, ['config']);
        container.register('promptFragmentManager', createPromptFragmentManagerModule, []);
        container.register('promptComposer', createPromptComposerModule, ['promptFragmentManager']);

        // 阶段 3: 注册依赖于业务逻辑的上层模块
        container.register('fieldMapper', createFieldMapperModule, ['gemini', 'channelDetector', 'addressTranslator', 'promptFragmentManager']);
        
        // 阶段 4: 注册UI和编辑器模块
        container.register('ruleEditor', createRuleEditorModule, ['config', 'channelDetector', 'localStorageManager']);
        container.register('promptEditor', createPromptEditorModule, ['config', 'fieldMapper', 'promptComposer', 'localStorageManager', 'gemini']);

        // 阶段 5: 注册主应用模块
        container.register('app', createApplicationModule, ['fieldMapper', 'channelDetector']);

        // 最终初始化
        console.log('⚙️ 所有模块注册完毕，开始初始化容器...');
        await container.initialize();
        console.log('✅ 统一初始化流程完成，应用已准备就绪。');

    } catch (error) {
        console.error('❌ 应用初始化过程中发生严重错误:', error);
        document.body.innerHTML = '<div style="color: red; text-align: center; padding: 50px;"><h1>Application Failed to Start</h1><p>A critical error occurred during initialization. Check the console for details.</p></div>';
    }
});
